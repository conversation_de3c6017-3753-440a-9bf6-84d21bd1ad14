import { createClient } from '@/lib/supabase/client'
import type { Receivable, Database } from '@/lib/types/database'

type ReceivableInsert = Database['public']['Tables']['receivables']['Insert']
type ReceivableUpdate = Database['public']['Tables']['receivables']['Update']

export async function getReceivables() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('receivables')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function getReceivable(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('receivables')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .eq('id', id)
    .single()

  return { data, error }
}

export async function createReceivable(receivable: ReceivableInsert) {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return { data: null, error: authError || new Error('User not authenticated') }
  }

  const { data, error } = await supabase
    .from('receivables')
    .insert({
      ...receivable,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateReceivable(id: string, updates: ReceivableUpdate) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('receivables')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteReceivable(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('receivables')
    .delete()
    .eq('id', id)

  return { error }
}

export async function getReceivableCategories() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('type', 'receivable')
    .order('name')

  return { data, error }
}

// Utility functions for receivable calculations
export function calculateTotalWithInterest(
  principal: number,
  annualRate: number,
  daysElapsed: number
): number {
  if (annualRate === 0) return principal
  
  const dailyRate = annualRate / 100 / 365
  const interest = principal * dailyRate * daysElapsed
  return principal + interest
}

export function getDaysOverdue(dueDate: string): number {
  const due = new Date(dueDate)
  const today = new Date()
  const diffTime = today.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}
