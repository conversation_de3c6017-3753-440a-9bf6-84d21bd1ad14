export const SUPPORTED_CURRENCIES = {
  LKR: {
    code: 'LK<PERSON>',
    name: 'Sri Lankan Rupee',
    symbol: 'Rs.',
    locale: 'si-LK'
  },
  USD: {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    locale: 'en-US'
  }
} as const

export type CurrencyCode = keyof typeof SUPPORTED_CURRENCIES

export function formatCurrencyWithCode(amount: number, currencyCode: CurrencyCode = 'LKR'): string {
  const currency = SUPPORTED_CURRENCIES[currencyCode]
  
  if (currencyCode === 'LKR') {
    // Format LKR with proper Sri Lankan formatting
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LKR',
      currencyDisplay: 'symbol',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount).replace('LKR', 'Rs.')
  }
  
  return new Intl.NumberFormat(currency.locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

export function getCurrencySymbol(currencyCode: CurrencyCode = 'LKR'): string {
  return SUPPORTED_CURRENCIES[currencyCode].symbol
}

export function getCurrencyName(currencyCode: CurrencyCode = 'LKR'): string {
  return SUPPORTED_CURRENCIES[currencyCode].name
}

// Exchange rate functionality (placeholder for future implementation)
export async function convertCurrency(
  amount: number, 
  fromCurrency: CurrencyCode, 
  toCurrency: CurrencyCode
): Promise<number> {
  // For now, return the same amount
  // In a real app, you would fetch exchange rates from an API
  if (fromCurrency === toCurrency) {
    return amount
  }
  
  // Placeholder conversion rates (these should come from a real API)
  const exchangeRates: Record<string, number> = {
    'LKR_USD': 0.0031, // 1 LKR = 0.0031 USD (approximate)
    'USD_LKR': 320,    // 1 USD = 320 LKR (approximate)
  }
  
  const rateKey = `${fromCurrency}_${toCurrency}`
  const rate = exchangeRates[rateKey]
  
  if (rate) {
    return amount * rate
  }
  
  return amount
}

export function getDefaultCurrency(): CurrencyCode {
  return 'LKR'
}
