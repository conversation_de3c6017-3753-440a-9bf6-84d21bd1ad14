# 🚨 Database Migration Required - Liability Types Enhancement

## Overview
The liability form has been enhanced to support different field requirements based on liability type. Some liability types (like insurance, utilities, rent) don't need principal amounts or current balances, while others (like loans, mortgages) do.

## Required Database Changes

### 1. Make Fields Optional
Run this SQL in your Supabase SQL Editor:

```sql
-- Make principal_amount and current_balance optional for recurring payment types
ALTER TABLE liabilities 
ALTER COLUMN principal_amount DROP NOT NULL,
ALTER COLUMN current_balance DROP NOT NULL;

-- Set default values for optional fields
ALTER TABLE liabilities 
ALTER COLUMN principal_amount SET DEFAULT 0,
ALTER COLUMN current_balance SET DEFAULT 0;
```

### 2. Verify Changes
Run this to verify the migration worked:

```sql
SELECT column_name, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'liabilities' 
AND column_name IN ('principal_amount', 'current_balance')
ORDER BY column_name;
```

Expected result:
- `current_balance`: is_nullable = YES, column_default = 0
- `principal_amount`: is_nullable = YES, column_default = 0

## What This Enables

### Debt-Based Liabilities (Require Principal/Balance):
- **Loan Taken**: Personal loans, business loans
- **Credit Card**: Credit card debt (balance required, principal optional)
- **Mortgage**: Home mortgages (both required, auto-recurring)
- **Education**: Student loans
- **Medical**: Medical bills (due date also required)

### Recurring Payment Liabilities (No Principal/Balance):
- **Insurance**: Insurance premiums
- **Utilities**: Electricity, water, gas bills
- **Taxes**: Tax payments
- **Subscription**: Netflix, Spotify, etc.
- **Rent**: Monthly rent payments

### Form Behavior Changes:
1. **Principal Amount**: Hidden for recurring payment types, optional/required for debt types
2. **Current Balance**: Hidden for recurring payment types, required for debt types
3. **Due Date**: Optional for all types (recurring payments use next_due_date instead)
4. **Auto-Recurring**: Automatically enabled for recurring payment types
5. **Frequency Suggestions**: Shows common frequencies for each liability type

## Testing After Migration

### Test Recurring Payment Types:
1. Create **Insurance** liability:
   - Principal/Balance fields should be hidden
   - Recurring should auto-enable
   - Common frequencies: Monthly, Quarterly, Yearly

2. Create **Utilities** liability:
   - Principal/Balance fields should be hidden
   - Recurring should auto-enable
   - Common frequencies: Weekly, Monthly

3. Create **Rent** liability:
   - Principal/Balance fields should be hidden
   - Recurring should auto-enable
   - Common frequency: Monthly

### Test Debt Types:
1. Create **Loan Taken** liability:
   - Principal Amount required
   - Current Balance required
   - Due Date optional
   - Recurring optional

2. Create **Credit Card** liability:
   - Principal Amount optional
   - Current Balance required
   - Due Date optional

3. Create **Medical** liability:
   - Principal Amount required
   - Current Balance required
   - Due Date required

## Rollback (If Needed)
If you need to rollback the changes:

```sql
-- Make fields required again (only if no NULL values exist)
ALTER TABLE liabilities 
ALTER COLUMN principal_amount SET NOT NULL,
ALTER COLUMN current_balance SET NOT NULL;

-- Remove default values
ALTER TABLE liabilities 
ALTER COLUMN principal_amount DROP DEFAULT,
ALTER COLUMN current_balance DROP DEFAULT;
```

**⚠️ Warning**: Only rollback if you haven't created any liabilities with NULL values for these fields.

## Benefits After Migration

1. **Better User Experience**: Forms adapt to liability type
2. **Cleaner Data**: No unnecessary fields for recurring payments
3. **Intuitive Interface**: Auto-suggestions based on liability type
4. **Flexible System**: Supports both debt tracking and recurring payments
5. **Type Safety**: Validation ensures appropriate data for each type

## Support

If you encounter any issues:
1. Check that the migration SQL ran successfully
2. Verify no existing data was affected
3. Test creating different liability types
4. Check that existing liabilities still display correctly

The application code has been updated to handle both nullable and non-nullable scenarios, so it should work even if the migration hasn't been run yet (with some limitations on form behavior).
