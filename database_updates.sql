-- Update liability types constraint to support more types
-- Run this in Supabase SQL Editor

-- Drop the existing constraint
ALTER TABLE liabilities DROP CONSTRAINT IF EXISTS liabilities_liability_type_check;

-- Add the new constraint with more liability types
ALTER TABLE liabilities ADD CONSTRAINT liabilities_liability_type_check
CHECK (liability_type IN (
  'loan_taken',
  'credit_card',
  'mortgage',
  'insurance',
  'utilities',
  'taxes',
  'subscription',
  'rent',
  'medical',
  'education',
  'other'
));

-- Verify the constraint was added
SELECT conname, consrc
FROM pg_constraint
WHERE conname = 'liabilities_liability_type_check';

-- ========================================
-- RECURRING PAYMENTS ENHANCEMENT
-- ========================================

-- Add recurring payment fields to liabilities table
ALTER TABLE liabilities
ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS recurring_frequency VARCHAR(20) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS recurring_amount DECIMAL(15,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS next_due_date DATE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_payment_date DATE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS recurring_start_date DATE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS recurring_end_date DATE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS auto_deduct BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS reminder_days INTEGER DEFAULT 3;

-- Add constraint for recurring frequency
ALTER TABLE liabilities
ADD CONSTRAINT liabilities_recurring_frequency_check
CHECK (recurring_frequency IS NULL OR recurring_frequency IN (
  'weekly',
  'monthly',
  'quarterly',
  'yearly'
));

-- Add constraint to ensure recurring fields are consistent
ALTER TABLE liabilities
ADD CONSTRAINT liabilities_recurring_consistency_check
CHECK (
  (is_recurring = FALSE AND recurring_frequency IS NULL AND recurring_amount IS NULL) OR
  (is_recurring = TRUE AND recurring_frequency IS NOT NULL AND recurring_amount IS NOT NULL)
);

-- Create index for efficient querying of recurring payments
CREATE INDEX IF NOT EXISTS idx_liabilities_recurring ON liabilities(is_recurring, next_due_date) WHERE is_recurring = TRUE;

-- Create index for due date queries
CREATE INDEX IF NOT EXISTS idx_liabilities_next_due_date ON liabilities(next_due_date) WHERE next_due_date IS NOT NULL;

-- Verify the new columns were added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'liabilities'
AND column_name IN ('is_recurring', 'recurring_frequency', 'recurring_amount', 'next_due_date', 'last_payment_date', 'recurring_start_date', 'recurring_end_date', 'auto_deduct', 'payment_method', 'reminder_days')
ORDER BY column_name;
