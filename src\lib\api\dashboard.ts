import { createClient } from '@/lib/supabase/client'
import { getRecurringPaymentsDue, calculateMonthlyRecurringTotal } from '@/lib/utils/recurringPayments'
import type { RecurringPaymentAlert } from '@/lib/utils/recurringPayments'

export interface DashboardData {
  totalAssets: number
  totalLiabilities: number
  totalReceivables: number
  netWorth: number
  assetsByType: { type: string; value: number; count: number }[]
  liabilitiesByType: { type: string; value: number; count: number }[]
  recentTransactions: any[]
  overdueItems: {
    liabilities: number
    receivables: number
  }
  recurringPayments: {
    monthlyTotal: number
    upcomingPayments: RecurringPaymentAlert[]
    overduePayments: RecurringPaymentAlert[]
    hasRecurringPayments: boolean
  }
}

export async function getDashboardData(): Promise<DashboardData> {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    throw new Error('User not authenticated')
  }

  // Get assets
  const { data: assets } = await supabase
    .from('assets')
    .select('current_value, asset_type, currency')
    .eq('user_id', user.id)

  // Get liabilities
  const { data: liabilities } = await supabase
    .from('liabilities')
    .select(`
      id,
      name,
      current_balance,
      liability_type,
      status,
      due_date,
      currency,
      is_recurring,
      recurring_frequency,
      recurring_amount,
      next_due_date,
      last_payment_date,
      recurring_start_date,
      recurring_end_date,
      auto_deduct,
      payment_method,
      reminder_days
    `)
    .eq('user_id', user.id)

  // Get receivables
  const { data: receivables } = await supabase
    .from('receivables')
    .select('current_balance, status, due_date, currency')
    .eq('user_id', user.id)

  // Get recent transactions
  const { data: transactions } = await supabase
    .from('transactions')
    .select(`
      *,
      categories (
        name,
        color
      )
    `)
    .eq('user_id', user.id)
    .order('transaction_date', { ascending: false })
    .limit(5)

  // Calculate totals
  const totalAssets = assets?.reduce((sum, asset) => sum + asset.current_value, 0) || 0
  const totalLiabilities = liabilities?.filter(l => l.status === 'active')
    .reduce((sum, liability) => sum + liability.current_balance, 0) || 0
  const totalReceivables = receivables?.filter(r => r.status === 'active')
    .reduce((sum, receivable) => sum + receivable.current_balance, 0) || 0

  const netWorth = totalAssets + totalReceivables - totalLiabilities

  // Group assets by type
  const assetsByType = assets?.reduce((acc, asset) => {
    const existing = acc.find(item => item.type === asset.asset_type)
    if (existing) {
      existing.value += asset.current_value
      existing.count += 1
    } else {
      acc.push({
        type: asset.asset_type,
        value: asset.current_value,
        count: 1
      })
    }
    return acc
  }, [] as { type: string; value: number; count: number }[]) || []

  // Group liabilities by type
  const liabilitiesByType = liabilities?.filter(l => l.status === 'active')
    .reduce((acc, liability) => {
      const existing = acc.find(item => item.type === liability.liability_type)
      if (existing) {
        existing.value += liability.current_balance
        existing.count += 1
      } else {
        acc.push({
          type: liability.liability_type,
          value: liability.current_balance,
          count: 1
        })
      }
      return acc
    }, [] as { type: string; value: number; count: number }[]) || []

  // Count overdue items
  const today = new Date()
  const overdueLiabilities = liabilities?.filter(l => 
    l.status === 'active' && l.due_date && new Date(l.due_date) < today
  ).length || 0

  const overdueReceivables = receivables?.filter(r =>
    r.status === 'active' && r.due_date && new Date(r.due_date) < today
  ).length || 0

  // Calculate recurring payment information
  const recurringLiabilities = liabilities?.filter(l => l.is_recurring && l.status === 'active') || []

  // Fix missing next_due_date and recurring_amount for existing recurring liabilities
  const fixedRecurringLiabilities = recurringLiabilities.map(liability => {
    let fixed = { ...liability }

    // Fix missing next_due_date
    if (liability.is_recurring && !liability.next_due_date && liability.due_date) {
      // Use the original due_date as next_due_date if missing
      fixed.next_due_date = liability.due_date
    } else if (liability.is_recurring && !liability.next_due_date && !liability.due_date) {
      // If no due date at all, set to today for immediate attention
      fixed.next_due_date = new Date().toISOString().split('T')[0]
    }

    // Fix missing recurring_amount - use current_balance as fallback
    if (liability.is_recurring && (!liability.recurring_amount || liability.recurring_amount === 0)) {
      fixed.recurring_amount = liability.current_balance
    }

    // Set default recurring_frequency if missing
    if (liability.is_recurring && !liability.recurring_frequency) {
      fixed.recurring_frequency = 'monthly' // Default to monthly
    }

    return fixed
  })

  const monthlyRecurringTotal = calculateMonthlyRecurringTotal(fixedRecurringLiabilities)
  const upcomingPayments = getRecurringPaymentsDue(fixedRecurringLiabilities, 7) // Next 7 days
  const overduePayments = upcomingPayments.filter(payment => payment.isOverdue)

  return {
    totalAssets,
    totalLiabilities,
    totalReceivables,
    netWorth,
    assetsByType,
    liabilitiesByType,
    recentTransactions: transactions || [],
    overdueItems: {
      liabilities: overdueLiabilities,
      receivables: overdueReceivables
    },
    recurringPayments: {
      monthlyTotal: monthlyRecurringTotal,
      upcomingPayments: upcomingPayments.filter(p => !p.isOverdue),
      overduePayments,
      hasRecurringPayments: fixedRecurringLiabilities.length > 0
    }
  }
}
