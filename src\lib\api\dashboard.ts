import { createClient } from '@/lib/supabase/client'

export interface DashboardData {
  totalAssets: number
  totalLiabilities: number
  totalReceivables: number
  netWorth: number
  assetsByType: { type: string; value: number; count: number }[]
  liabilitiesByType: { type: string; value: number; count: number }[]
  recentTransactions: any[]
  overdueItems: {
    liabilities: number
    receivables: number
  }
}

export async function getDashboardData(): Promise<DashboardData> {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    throw new Error('User not authenticated')
  }

  // Get assets
  const { data: assets } = await supabase
    .from('assets')
    .select('current_value, asset_type, currency')
    .eq('user_id', user.id)

  // Get liabilities
  const { data: liabilities } = await supabase
    .from('liabilities')
    .select('current_balance, liability_type, status, due_date, currency')
    .eq('user_id', user.id)

  // Get receivables
  const { data: receivables } = await supabase
    .from('receivables')
    .select('current_balance, status, due_date, currency')
    .eq('user_id', user.id)

  // Get recent transactions
  const { data: transactions } = await supabase
    .from('transactions')
    .select(`
      *,
      categories (
        name,
        color
      )
    `)
    .eq('user_id', user.id)
    .order('transaction_date', { ascending: false })
    .limit(5)

  // Calculate totals
  const totalAssets = assets?.reduce((sum, asset) => sum + asset.current_value, 0) || 0
  const totalLiabilities = liabilities?.filter(l => l.status === 'active')
    .reduce((sum, liability) => sum + liability.current_balance, 0) || 0
  const totalReceivables = receivables?.filter(r => r.status === 'active')
    .reduce((sum, receivable) => sum + receivable.current_balance, 0) || 0

  const netWorth = totalAssets + totalReceivables - totalLiabilities

  // Group assets by type
  const assetsByType = assets?.reduce((acc, asset) => {
    const existing = acc.find(item => item.type === asset.asset_type)
    if (existing) {
      existing.value += asset.current_value
      existing.count += 1
    } else {
      acc.push({
        type: asset.asset_type,
        value: asset.current_value,
        count: 1
      })
    }
    return acc
  }, [] as { type: string; value: number; count: number }[]) || []

  // Group liabilities by type
  const liabilitiesByType = liabilities?.filter(l => l.status === 'active')
    .reduce((acc, liability) => {
      const existing = acc.find(item => item.type === liability.liability_type)
      if (existing) {
        existing.value += liability.current_balance
        existing.count += 1
      } else {
        acc.push({
          type: liability.liability_type,
          value: liability.current_balance,
          count: 1
        })
      }
      return acc
    }, [] as { type: string; value: number; count: number }[]) || []

  // Count overdue items
  const today = new Date()
  const overdueLiabilities = liabilities?.filter(l => 
    l.status === 'active' && l.due_date && new Date(l.due_date) < today
  ).length || 0

  const overdueReceivables = receivables?.filter(r => 
    r.status === 'active' && r.due_date && new Date(r.due_date) < today
  ).length || 0

  return {
    totalAssets,
    totalLiabilities,
    totalReceivables,
    netWorth,
    assetsByType,
    liabilitiesByType,
    recentTransactions: transactions || [],
    overdueItems: {
      liabilities: overdueLiabilities,
      receivables: overdueReceivables
    }
  }
}
