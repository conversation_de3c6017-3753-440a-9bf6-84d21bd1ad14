'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createCategory, updateCategory } from '@/lib/api/categories'
import type { Category } from '@/lib/types/database'

interface CategoryFormProps {
  category?: Category
  onSuccess: () => void
  onCancel: () => void
}

const colorOptions = [
  { value: '#EF4444', label: 'Red', bg: 'bg-red-500' },
  { value: '#F59E0B', label: 'Orange', bg: 'bg-orange-500' },
  { value: '#EAB308', label: 'Yellow', bg: 'bg-yellow-500' },
  { value: '#10B981', label: 'Green', bg: 'bg-green-500' },
  { value: '#3B82F6', label: 'Blue', bg: 'bg-blue-500' },
  { value: '#8B5CF6', label: 'Purple', bg: 'bg-purple-500' },
  { value: '#EC4899', label: 'Pink', bg: 'bg-pink-500' },
  { value: '#6B7280', label: 'Gray', bg: 'bg-gray-500' },
]

export default function CategoryForm({ category, onSuccess, onCancel }: CategoryFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: category?.name || '',
    type: category?.type || 'expense' as const,
    color: category?.color || '#3B82F6',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (category) {
        await updateCategory(category.id, formData as any)
      } else {
        await createCategory(formData as any)
      }
      onSuccess()
    } catch (error) {
      console.error('Error saving category:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
    >
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center">
            <div 
              className="w-4 h-4 rounded-full mr-3"
              style={{ backgroundColor: formData.color }}
            />
            {category ? 'Edit Category' : 'Add New Category'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="name">Category Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Enter category name"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="type">Category Type *</Label>
              <Select
                id="type"
                value={formData.type}
                onChange={(e) => handleChange('type', e.target.value)}
                required
              >
                <option value="income">Income</option>
                <option value="expense">Expense</option>
                <option value="asset">Asset</option>
                <option value="liability">Liability</option>
                <option value="receivable">Receivable</option>
              </Select>
            </div>

            <div>
              <Label htmlFor="color">Color</Label>
              <div className="grid grid-cols-4 gap-3 mt-2">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    type="button"
                    onClick={() => handleChange('color', color.value)}
                    className={`
                      w-full h-12 rounded-xl ${color.bg} flex items-center justify-center
                      ${formData.color === color.value 
                        ? 'ring-4 ring-blue-500 ring-offset-2' 
                        : 'hover:scale-105'
                      }
                      transition-all duration-200
                    `}
                  >
                    {formData.color === color.value && (
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                {category ? 'Update Category' : 'Create Category'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
