# Recurring Payments Testing Guide

## 🚀 **Pre-Testing Setup**

### 1. **Database Migration**
Before testing, ensure you've run the database migration:

```sql
-- In Supabase SQL Editor, copy and paste the entire content from database_updates.sql
-- This adds the recurring payment fields to the liabilities table
```

### 2. **Verify Schema**
Check that new columns exist:
```sql
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'liabilities' 
AND column_name LIKE '%recurring%' OR column_name LIKE '%due_date%'
ORDER BY column_name;
```

## 🧪 **Testing Scenarios**

### **Scenario 1: Create Monthly Rent Payment**
1. **Navigate to Liabilities** → Click "Add Liability"
2. **Fill Basic Info**:
   - Name: "Monthly Rent"
   - Type: "Rent"
   - Principal Amount: 50000
   - Current Balance: 50000
3. **Enable Recurring**:
   - ✅ Check "This is a recurring payment"
   - Frequency: "Monthly"
   - Payment Amount: 50000
   - Start Date: Current date
   - Reminder: 3 days
4. **Submit** → Verify success message
5. **Check Dashboard** → Should show in recurring payments section

**Expected Results**:
- ✅ Liability created successfully
- ✅ Next due date auto-calculated (1 month from start date)
- ✅ Appears in dashboard recurring payments
- ✅ Shows monthly total updated

### **Scenario 2: Create Weekly Utility Bill**
1. **Add New Liability**:
   - Name: "Electricity Bill"
   - Type: "Utilities"
   - Principal: 10000
   - Current Balance: 2500
2. **Recurring Settings**:
   - ✅ Enable recurring
   - Frequency: "Weekly"
   - Payment Amount: 2500
   - Auto-deduct: ✅ Yes
   - Payment Method: "Bank Auto-debit"
3. **Submit and Verify**

**Expected Results**:
- ✅ Weekly frequency calculated correctly
- ✅ Next due date is 7 days from start
- ✅ Auto-deduct setting saved
- ✅ Payment method stored

### **Scenario 3: Mark Payment as Paid**
1. **Go to Liabilities List**
2. **Find Recurring Payment** with due date
3. **Click Credit Card Icon** (Mark as Paid)
4. **Confirm Payment**
5. **Verify Updates**:
   - Last payment date updated
   - Next due date advanced by frequency period
   - Dashboard reflects changes

**Expected Results**:
- ✅ Payment marked successfully
- ✅ Next due date auto-calculated
- ✅ Last payment date recorded
- ✅ Dashboard updates immediately

### **Scenario 4: Edit Recurring Payment**
1. **Edit Existing Recurring Liability**
2. **Change Frequency** from Monthly to Quarterly
3. **Update Payment Amount**
4. **Save Changes**

**Expected Results**:
- ✅ Frequency updated successfully
- ✅ Next due date recalculated
- ✅ Payment amount updated
- ✅ Dashboard reflects new monthly total

### **Scenario 5: Disable Recurring Payment**
1. **Edit Recurring Liability**
2. **Uncheck "This is a recurring payment"**
3. **Save Changes**

**Expected Results**:
- ✅ Recurring fields cleared
- ✅ Removed from dashboard recurring section
- ✅ Still appears as regular liability
- ✅ Monthly total updated

## 📱 **Mobile Testing**

### **Mobile Liability List**
1. **Open on Mobile Device**
2. **Navigate to Liabilities**
3. **Verify Mobile Cards Show**:
   - ✅ Recurring payment indicator
   - ✅ Payment amount
   - ✅ Next due date
   - ✅ Mark as paid button

### **Mobile Dashboard**
1. **Check Recurring Payments Card**
2. **Verify Responsive Design**:
   - ✅ Monthly total visible
   - ✅ Upcoming payments list
   - ✅ Overdue alerts
   - ✅ Touch-friendly buttons

## 🔍 **Edge Cases Testing**

### **Case 1: Invalid Data**
1. **Try to create recurring payment without**:
   - Frequency (should show error)
   - Payment amount (should show error)
   - Start date (should show error)
2. **Try end date before start date** (should show error)

### **Case 2: Large Numbers**
1. **Enter very large payment amount** (> 999,999,999,999)
2. **Should show overflow error**

### **Case 3: Past Due Dates**
1. **Create recurring payment with past start date**
2. **Should appear as overdue in dashboard**

### **Case 4: Future End Dates**
1. **Create recurring payment with end date**
2. **Verify it doesn't generate payments beyond end date**

## 📊 **Dashboard Integration Testing**

### **Monthly Total Calculation**
1. **Create multiple recurring payments**:
   - Weekly: Rs. 2,500 (should add Rs. 10,825/month)
   - Monthly: Rs. 50,000 (should add Rs. 50,000/month)
   - Quarterly: Rs. 15,000 (should add Rs. 5,000/month)
   - Yearly: Rs. 60,000 (should add Rs. 5,000/month)
2. **Total should be**: Rs. 70,825/month

### **Upcoming Payments**
1. **Create payments due in next 7 days**
2. **Verify they appear in upcoming section**
3. **Create overdue payments**
4. **Verify they appear in alerts**

### **Alerts Integration**
1. **Create overdue recurring payment**
2. **Check dashboard alerts section**
3. **Should show recurring payment alert**

## 🔄 **Data Consistency Testing**

### **Net Worth Calculation**
1. **Verify recurring payments don't affect net worth calculation**
2. **Net worth should still be**: Assets + Receivables - Liabilities
3. **Recurring amount should not double-count**

### **Liability Balance vs Recurring Amount**
1. **Current balance**: Total debt amount
2. **Recurring amount**: Individual payment amount
3. **These should be independent values**

## 🚨 **Error Handling Testing**

### **Network Errors**
1. **Disconnect internet**
2. **Try to mark payment as paid**
3. **Should show appropriate error message**

### **Database Errors**
1. **Test with invalid liability ID**
2. **Should handle gracefully**

### **Form Validation**
1. **Submit form with missing required fields**
2. **Should show validation errors**
3. **Should not submit invalid data**

## ✅ **Success Criteria**

### **Functionality**
- [ ] Can create recurring payments with all frequencies
- [ ] Can mark payments as paid successfully
- [ ] Next due dates calculate correctly
- [ ] Dashboard shows accurate information
- [ ] Mobile interface works properly
- [ ] Form validation prevents invalid data

### **Performance**
- [ ] Dashboard loads quickly with recurring data
- [ ] Liability list performs well with recurring info
- [ ] No noticeable slowdown in existing features

### **User Experience**
- [ ] Intuitive form design
- [ ] Clear visual indicators
- [ ] Helpful error messages
- [ ] Responsive design works well

### **Data Integrity**
- [ ] No existing data is affected
- [ ] Net worth calculations remain accurate
- [ ] All CRUD operations work correctly
- [ ] Database constraints prevent invalid data

## 🐛 **Common Issues & Solutions**

### **Issue**: Next due date not calculating
**Solution**: Check that recurring_frequency and recurring_start_date are set

### **Issue**: Dashboard not showing recurring payments
**Solution**: Verify liability has is_recurring = true and status = 'active'

### **Issue**: Mark as paid not working
**Solution**: Check that liability has valid next_due_date

### **Issue**: Form validation errors
**Solution**: Ensure all required recurring fields are filled when is_recurring is true

## 📝 **Test Results Template**

```
Test Date: ___________
Tester: ___________

✅ Basic Functionality
- [ ] Create monthly recurring payment
- [ ] Create weekly recurring payment
- [ ] Mark payment as paid
- [ ] Edit recurring settings
- [ ] Disable recurring payment

✅ Dashboard Integration
- [ ] Monthly total calculation
- [ ] Upcoming payments display
- [ ] Overdue alerts
- [ ] Navigation to liabilities

✅ Mobile Testing
- [ ] Responsive design
- [ ] Touch interactions
- [ ] Card layout

✅ Edge Cases
- [ ] Invalid data handling
- [ ] Large numbers
- [ ] Past due dates

Notes:
_________________________________
_________________________________
_________________________________

Overall Status: PASS / FAIL
```

## 🎯 **Post-Testing Verification**

After completing all tests:

1. **Check Database**: Verify all test data is properly stored
2. **Performance**: Ensure no performance degradation
3. **Existing Features**: Verify all existing functionality still works
4. **Clean Up**: Remove test data if needed
5. **Documentation**: Update any findings in this document

**The recurring payments feature is ready for production when all test scenarios pass successfully!**
