'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'
import { Button } from './Button'
import QuickTransactionForm from '@/components/transactions/QuickTransactionForm'

export default function QuickActionButton() {
  const [isOpen, setIsOpen] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [selectedType, setSelectedType] = useState<'income' | 'expense' | 'transfer'>('expense')

  const quickActions = [
    { type: 'income' as const, icon: Plus, label: 'Income', color: 'bg-green-500 hover:bg-green-600' },
    { type: 'expense' as const, icon: Minus, label: 'Expense', color: 'bg-red-500 hover:bg-red-600' },
    { type: 'transfer' as const, icon: ArrowRightLeft, label: 'Transfer', color: 'bg-blue-500 hover:bg-blue-600' },
  ]

  const handleActionClick = (type: 'income' | 'expense' | 'transfer') => {
    setSelectedType(type)
    setShowForm(true)
    setIsOpen(false)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    // Optionally trigger a refresh of the current page data
    window.location.reload()
  }

  return (
    <>
      <div className="fixed bottom-6 right-6 z-40">
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute bottom-16 right-0 space-y-3"
            >
              {quickActions.map((action, index) => (
                <motion.button
                  key={action.type}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleActionClick(action.type)}
                  className={`
                    flex items-center space-x-3 px-4 py-3 rounded-xl text-white font-medium
                    shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105
                    ${action.color}
                  `}
                >
                  <action.icon className="h-5 w-5" />
                  <span className="whitespace-nowrap">{action.label}</span>
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setIsOpen(!isOpen)}
          className={`
            w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200
            flex items-center justify-center text-white font-bold text-xl
            ${isOpen 
              ? 'bg-gray-500 hover:bg-gray-600' 
              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
            }
          `}
        >
          <motion.div
            animate={{ rotate: isOpen ? 45 : 0 }}
            transition={{ duration: 0.2 }}
          >
            {isOpen ? <X className="h-6 w-6" /> : <Plus className="h-6 w-6" />}
          </motion.div>
        </motion.button>
      </div>

      <AnimatePresence>
        {showForm && (
          <QuickTransactionForm
            defaultType={selectedType}
            onSuccess={handleFormSuccess}
            onCancel={() => setShowForm(false)}
          />
        )}
      </AnimatePresence>
    </>
  )
}
