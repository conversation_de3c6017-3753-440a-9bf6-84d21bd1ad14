import { useState } from 'react'
import { ChevronDown, Filter } from 'lucide-react'
import { Button } from './Button'
import { Select } from './Select'
import { Input } from './Input'
import { Label } from './Label'

interface FilterOption {
  key: string
  label: string
  type: 'select' | 'date' | 'number'
  options?: { value: string; label: string }[]
}

interface FilterDropdownProps {
  filters: FilterOption[]
  values: Record<string, any>
  onChange: (key: string, value: any) => void
  onClear: () => void
}

export default function FilterDropdown({ 
  filters, 
  values, 
  onChange, 
  onClear 
}: FilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const activeFiltersCount = Object.values(values).filter(v => v !== '' && v !== null && v !== undefined).length

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center"
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
        {activeFiltersCount > 0 && (
          <span className="ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
            {activeFiltersCount}
          </span>
        )}
        <ChevronDown className="h-4 w-4 ml-2" />
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Filters</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  onClear()
                  setIsOpen(false)
                }}
              >
                Clear All
              </Button>
            </div>

            {filters.map((filter) => (
              <div key={filter.key}>
                <Label htmlFor={filter.key}>{filter.label}</Label>
                {filter.type === 'select' && filter.options ? (
                  <Select
                    id={filter.key}
                    value={values[filter.key] || ''}
                    onChange={(e) => onChange(filter.key, e.target.value)}
                  >
                    <option value="">All</option>
                    {filter.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Select>
                ) : (
                  <Input
                    id={filter.key}
                    type={filter.type === 'date' ? 'date' : filter.type === 'number' ? 'number' : 'text'}
                    value={values[filter.key] || ''}
                    onChange={(e) => onChange(filter.key, e.target.value)}
                  />
                )}
              </div>
            ))}

            <div className="flex justify-end pt-2">
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
