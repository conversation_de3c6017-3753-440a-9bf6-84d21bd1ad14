'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createAsset, updateAsset } from '@/lib/api/assets'
import { createLiability } from '@/lib/api/liabilities'
import type { Asset } from '@/lib/types/database'

interface AssetFormProps {
  asset?: Asset
  onSuccess: () => void
  onCancel: () => void
}

export default function AssetForm({ asset, onSuccess, onCancel }: AssetFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: asset?.name || '',
    description: asset?.description || '',
    current_value: asset?.current_value || 0,
    purchase_value: asset?.purchase_value || 0,
    purchase_date: asset?.purchase_date || '',
    asset_type: asset?.asset_type || 'investment' as const,
    currency: asset?.currency || 'LKR',
  })

  const [hasLoan, setHasLoan] = useState(false)
  const [loanData, setLoanData] = useState({
    name: '',
    principal_amount: 0,
    current_balance: 0,
    interest_rate: 0,
    due_date: '',
    description: ''
  })



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let result
      if (asset) {
        result = await updateAsset(asset.id, formData)
      } else {
        result = await createAsset(formData as any)
      }

      if (result.error) {
        console.error('Error saving asset:', result.error)
        alert('Error saving asset: ' + result.error.message)
        return
      }

      // If this is a real estate or vehicle with a loan, create the liability
      if (hasLoan && (formData.asset_type === 'real_estate' || formData.asset_type === 'vehicle') && !asset) {
        const loanResult = await createLiability({
          name: loanData.name || `${formData.name} Loan`,
          liability_type: formData.asset_type === 'real_estate' ? 'mortgage' : 'loan_taken',
          principal_amount: loanData.principal_amount,
          current_balance: loanData.current_balance || loanData.principal_amount,
          interest_rate: loanData.interest_rate,
          due_date: loanData.due_date,
          currency: formData.currency,
          description: loanData.description || `Loan for ${formData.name}`
        } as any)

        if (loanResult.error) {
          console.error('Error creating loan:', loanResult.error)
          alert('Asset created but failed to create loan: ' + loanResult.error.message)
        }
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving asset:', error)
      alert('Error saving asset: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    // Validate numeric fields to prevent overflow
    if ((field === 'current_value' || field === 'purchase_value') && typeof value === 'number') {
      // Limit to reasonable values to prevent database overflow
      if (value > 999999999999) {
        alert('Value is too large. Please enter a smaller amount.')
        return
      }
    }
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{asset ? 'Edit Asset' : 'Add New Asset'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Asset Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="asset_type">Asset Type *</Label>
              <Select
                id="asset_type"
                value={formData.asset_type}
                onChange={(e) => handleChange('asset_type', e.target.value)}
                required
              >
                <option value="investment">Investment</option>
                <option value="real_estate">Real Estate</option>
                <option value="vehicle">Vehicle</option>
                <option value="cash">Cash</option>
                <option value="savings">Savings Account</option>
                <option value="fixed_deposit">Fixed Deposit</option>
                <option value="stocks">Stocks</option>
                <option value="bonds">Bonds</option>
                <option value="mutual_funds">Mutual Funds</option>
                <option value="cryptocurrency">Cryptocurrency</option>
                <option value="jewelry">Jewelry</option>
                <option value="art_collectibles">Art & Collectibles</option>
                <option value="business">Business</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="current_value">Current Value *</Label>
              <Input
                id="current_value"
                type="number"
                step="0.01"
                value={formData.current_value}
                onChange={(e) => handleChange('current_value', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="purchase_value">Purchase Value</Label>
              <Input
                id="purchase_value"
                type="number"
                step="0.01"
                value={formData.purchase_value}
                onChange={(e) => handleChange('purchase_value', parseFloat(e.target.value) || 0)}
              />
            </div>
            
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
              >
                <option value="LKR">LKR (Sri Lankan Rupee)</option>
                <option value="USD">USD (US Dollar)</option>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="purchase_date">Purchase Date</Label>
              <Input
                id="purchase_date"
                type="date"
                value={formData.purchase_date}
                onChange={(e) => handleChange('purchase_date', e.target.value)}
              />
            </div>
            

          </div>

          {/* Loan/Mortgage Section for Real Estate and Vehicle */}
          {(formData.asset_type === 'real_estate' || formData.asset_type === 'vehicle') && !asset && (
            <div className="border-t pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="checkbox"
                  id="hasLoan"
                  checked={hasLoan}
                  onChange={(e) => setHasLoan(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="hasLoan">
                  This {formData.asset_type === 'real_estate' ? 'property has a mortgage' : 'vehicle has a loan'}
                </Label>
              </div>

              {hasLoan && (
                <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-lg">
                    {formData.asset_type === 'real_estate' ? 'Mortgage' : 'Vehicle Loan'} Details
                  </h3>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="loanName">Loan Name</Label>
                      <Input
                        id="loanName"
                        value={loanData.name}
                        onChange={(e) => setLoanData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder={`${formData.name} ${formData.asset_type === 'real_estate' ? 'Mortgage' : 'Loan'}`}
                      />
                    </div>

                    <div>
                      <Label htmlFor="principalAmount">Principal Amount *</Label>
                      <Input
                        id="principalAmount"
                        type="number"
                        step="0.01"
                        value={loanData.principal_amount}
                        onChange={(e) => setLoanData(prev => ({ ...prev, principal_amount: parseFloat(e.target.value) || 0 }))}
                        required={hasLoan}
                      />
                    </div>

                    <div>
                      <Label htmlFor="currentBalance">Current Balance</Label>
                      <Input
                        id="currentBalance"
                        type="number"
                        step="0.01"
                        value={loanData.current_balance}
                        onChange={(e) => setLoanData(prev => ({ ...prev, current_balance: parseFloat(e.target.value) || 0 }))}
                        placeholder="Leave empty to use principal amount"
                      />
                    </div>

                    <div>
                      <Label htmlFor="interestRate">Interest Rate (%)</Label>
                      <Input
                        id="interestRate"
                        type="number"
                        step="0.01"
                        value={loanData.interest_rate}
                        onChange={(e) => setLoanData(prev => ({ ...prev, interest_rate: parseFloat(e.target.value) || 0 }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="loanDueDate">Due Date</Label>
                      <Input
                        id="loanDueDate"
                        type="date"
                        value={loanData.due_date}
                        onChange={(e) => setLoanData(prev => ({ ...prev, due_date: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="loanDescription">Loan Description</Label>
                    <Textarea
                      id="loanDescription"
                      value={loanData.description}
                      onChange={(e) => setLoanData(prev => ({ ...prev, description: e.target.value }))}
                      rows={2}
                      placeholder="Additional details about the loan..."
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          <div>
            <Label htmlFor="description">Asset Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : asset ? 'Update Asset' : 'Create Asset'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
