'use client'

import { useState, useEffect, useMemo } from 'react'
import { Edit, Trash2, Plus, Alert<PERSON>riangle, Clock } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import SearchInput from '@/components/ui/SearchInput'
import FilterDropdown from '@/components/ui/FilterDropdown'
import { getReceivables, deleteReceivable, getDaysOverdue } from '@/lib/api/receivables'
import { formatCurrency, formatDate } from '@/lib/utils'
import ReceivableForm from './ReceivableForm'
import type { Receivable } from '@/lib/types/database'

export default function ReceivableList() {
  const [receivables, setReceivables] = useState<Receivable[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingReceivable, setEditingReceivable] = useState<Receivable | undefined>()
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    status: '',
    min_balance: '',
    max_balance: '',
    due_date_from: '',
    due_date_to: ''
  })

  useEffect(() => {
    loadReceivables()
  }, [])

  const loadReceivables = async () => {
    setLoading(true)
    const { data } = await getReceivables()
    if (data) setReceivables(data)
    setLoading(false)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this receivable?')) {
      await deleteReceivable(id)
      loadReceivables()
    }
  }

  const handleEdit = (receivable: Receivable) => {
    setEditingReceivable(receivable)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingReceivable(undefined)
    loadReceivables()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingReceivable(undefined)
  }

  // Filter and search receivables
  const filteredReceivables = useMemo(() => {
    return receivables.filter(receivable => {
      // Search filter
      const matchesSearch = searchTerm === '' ||
        receivable.debtor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        receivable.debtor_contact?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        receivable.description?.toLowerCase().includes(searchTerm.toLowerCase())

      // Status filter
      const matchesStatus = filters.status === '' || receivable.status === filters.status

      // Balance filters
      const matchesMinBalance = filters.min_balance === '' || receivable.current_balance >= parseFloat(filters.min_balance)
      const matchesMaxBalance = filters.max_balance === '' || receivable.current_balance <= parseFloat(filters.max_balance)

      // Date filters
      const matchesFromDate = filters.due_date_from === '' ||
        !receivable.due_date ||
        new Date(receivable.due_date) >= new Date(filters.due_date_from)
      const matchesToDate = filters.due_date_to === '' ||
        !receivable.due_date ||
        new Date(receivable.due_date) <= new Date(filters.due_date_to)

      return matchesSearch && matchesStatus && matchesMinBalance && matchesMaxBalance && matchesFromDate && matchesToDate
    })
  }, [receivables, searchTerm, filters])

  const totalBalance = filteredReceivables
    .filter(r => r.status === 'active')
    .reduce((sum, receivable) => sum + receivable.current_balance, 0)

  const overdueReceivables = filteredReceivables.filter(r =>
    r.status === 'active' &&
    r.due_date &&
    new Date(r.due_date) < new Date()
  )

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: 'active', label: 'Active' },
        { value: 'paid', label: 'Paid' },
        { value: 'written_off', label: 'Written Off' }
      ]
    },
    {
      key: 'min_balance',
      label: 'Minimum Balance',
      type: 'number' as const
    },
    {
      key: 'max_balance',
      label: 'Maximum Balance',
      type: 'number' as const
    },
    {
      key: 'due_date_from',
      label: 'Due Date From',
      type: 'date' as const
    },
    {
      key: 'due_date_to',
      label: 'Due Date To',
      type: 'date' as const
    }
  ]

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleClearFilters = () => {
    setFilters({
      status: '',
      min_balance: '',
      max_balance: '',
      due_date_from: '',
      due_date_to: ''
    })
    setSearchTerm('')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600'
      case 'paid': return 'text-blue-600'
      case 'written_off': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (receivable: Receivable) => {
    if (receivable.status !== 'active' || !receivable.due_date) return null
    
    const daysOverdue = getDaysOverdue(receivable.due_date)
    if (daysOverdue > 0) {
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    }
    
    const daysUntilDue = Math.ceil((new Date(receivable.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    if (daysUntilDue <= 7) {
      return <Clock className="h-4 w-4 text-yellow-500" />
    }
    
    return null
  }

  if (showForm) {
    return (
      <div className="flex justify-center">
        <ReceivableForm
          receivable={editingReceivable}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Receivables</h1>
          <p className="text-gray-600">
            Total Outstanding: {formatCurrency(totalBalance)}
            {filteredReceivables.length !== receivables.length && (
              <span className="text-sm text-gray-500 ml-2">
                ({filteredReceivables.length} of {receivables.length} shown)
              </span>
            )}
          </p>
          {overdueReceivables.length > 0 && (
            <p className="text-red-600 flex items-center mt-1">
              <AlertTriangle className="h-4 w-4 mr-1" />
              {overdueReceivables.length} overdue receivable(s)
            </p>
          )}
        </div>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Receivable
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search receivables..."
          className="flex-1"
        />
        <FilterDropdown
          filters={filterOptions}
          values={filters}
          onChange={handleFilterChange}
          onClear={handleClearFilters}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Money Owed to You</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredReceivables.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {receivables.length === 0
                ? "No receivables found. Add your first receivable to get started."
                : "No receivables match your search criteria."
              }
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Debtor</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Principal</TableHead>
                      <TableHead>Current Balance</TableHead>
                      <TableHead>Interest Rate</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReceivables.map((receivable) => {
                      const isOverdue = receivable.status === 'active' &&
                                      receivable.due_date &&
                                      new Date(receivable.due_date) < new Date()

                      return (
                        <TableRow key={receivable.id} className={isOverdue ? 'bg-red-50' : ''}>
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              {receivable.debtor_name}
                              {getStatusIcon(receivable) && (
                                <span className="ml-2">{getStatusIcon(receivable)}</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {receivable.debtor_contact || '-'}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(receivable.principal_amount, receivable.currency)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(receivable.current_balance, receivable.currency)}
                          </TableCell>
                          <TableCell>
                            {receivable.interest_rate ? `${receivable.interest_rate}%` : '-'}
                          </TableCell>
                          <TableCell>
                            {receivable.due_date ? (
                              <div>
                                {formatDate(receivable.due_date)}
                                {receivable.status === 'active' && receivable.due_date && (
                                  <div className="text-xs text-gray-500">
                                    {(() => {
                                      const daysOverdue = getDaysOverdue(receivable.due_date)
                                      if (daysOverdue > 0) {
                                        return `${daysOverdue} days overdue`
                                      }
                                      const daysUntilDue = Math.ceil((new Date(receivable.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                                      return daysUntilDue > 0 ? `${daysUntilDue} days left` : 'Due today'
                                    })()}
                                  </div>
                                )}
                              </div>
                            ) : '-'}
                          </TableCell>
                          <TableCell>
                            <span className={`capitalize ${getStatusColor(receivable.status)}`}>
                              {receivable.status.replace('_', ' ')}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEdit(receivable)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDelete(receivable.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="lg:hidden space-y-4">
                {filteredReceivables.map((receivable) => {
                  const isOverdue = receivable.status === 'active' &&
                                  receivable.due_date &&
                                  new Date(receivable.due_date) < new Date()

                  return (
                    <div key={receivable.id} className={`bg-white border rounded-lg p-4 shadow-sm ${isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-medium text-gray-900 flex items-center">
                            {receivable.debtor_name}
                            {getStatusIcon(receivable) && (
                              <span className="ml-2">{getStatusIcon(receivable)}</span>
                            )}
                          </h3>
                          <p className="text-sm text-gray-500">{receivable.debtor_contact || 'No contact info'}</p>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(receivable)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(receivable.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Principal:</span>
                          <span className="text-sm font-medium">{formatCurrency(receivable.principal_amount, receivable.currency)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Current Balance:</span>
                          <span className="text-sm font-medium">{formatCurrency(receivable.current_balance, receivable.currency)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Interest Rate:</span>
                          <span className="text-sm font-medium">{receivable.interest_rate ? `${receivable.interest_rate}%` : '-'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Due Date:</span>
                          <span className="text-sm font-medium">
                            {receivable.due_date ? (
                              <div>
                                {formatDate(receivable.due_date)}
                                {receivable.status === 'active' && receivable.due_date && (
                                  <div className="text-xs text-gray-500">
                                    {(() => {
                                      const daysOverdue = getDaysOverdue(receivable.due_date)
                                      if (daysOverdue > 0) {
                                        return `${daysOverdue} days overdue`
                                      }
                                      const daysUntilDue = Math.ceil((new Date(receivable.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                                      return daysUntilDue > 0 ? `${daysUntilDue} days left` : 'Due today'
                                    })()}
                                  </div>
                                )}
                              </div>
                            ) : '-'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Status:</span>
                          <span className={`text-sm font-medium capitalize ${getStatusColor(receivable.status)}`}>
                            {receivable.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </>
          )}
          )}
        </CardContent>
      </Card>
    </div>
  )
}
