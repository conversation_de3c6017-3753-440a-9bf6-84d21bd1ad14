export interface Database {
  public: {
    Tables: {
      transactions: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          type: 'income' | 'expense' | 'transfer'
          amount: number
          description: string | null
          transaction_date: string
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          type: 'income' | 'expense' | 'transfer'
          amount: number
          description?: string | null
          transaction_date: string
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          type?: 'income' | 'expense' | 'transfer'
          amount?: number
          description?: string | null
          transaction_date?: string
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          preferred_currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          preferred_currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          preferred_currency?: string
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'asset' | 'liability' | 'receivable'
          color: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'asset' | 'liability' | 'receivable'
          color?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'asset' | 'liability' | 'receivable'
          color?: string
          created_at?: string
          updated_at?: string
        }
      }
      assets: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          name: string
          description: string | null
          current_value: number
          purchase_value: number | null
          purchase_date: string | null
          asset_type: 'investment' | 'real_estate' | 'vehicle' | 'cash' | 'other'
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          name: string
          description?: string | null
          current_value?: number
          purchase_value?: number | null
          purchase_date?: string | null
          asset_type: 'investment' | 'real_estate' | 'vehicle' | 'cash' | 'other'
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          current_value?: number
          purchase_value?: number | null
          purchase_date?: string | null
          asset_type?: 'investment' | 'real_estate' | 'vehicle' | 'cash' | 'other'
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
      liabilities: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          name: string
          description: string | null
          principal_amount: number
          current_balance: number
          interest_rate: number | null
          due_date: string | null
          liability_type: 'loan_taken' | 'credit_card' | 'mortgage' | 'insurance' | 'utilities' | 'taxes' | 'subscription' | 'rent' | 'medical' | 'education' | 'other'
          status: 'active' | 'paid_off' | 'defaulted'
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          name: string
          description?: string | null
          principal_amount: number
          current_balance: number
          interest_rate?: number | null
          due_date?: string | null
          liability_type: 'loan_taken' | 'credit_card' | 'mortgage' | 'insurance' | 'utilities' | 'taxes' | 'subscription' | 'rent' | 'medical' | 'education' | 'other'
          status?: 'active' | 'paid_off' | 'defaulted'
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          principal_amount?: number
          current_balance?: number
          interest_rate?: number | null
          due_date?: string | null
          liability_type?: 'loan_taken' | 'credit_card' | 'mortgage' | 'insurance' | 'utilities' | 'taxes' | 'subscription' | 'rent' | 'medical' | 'education' | 'other'
          status?: 'active' | 'paid_off' | 'defaulted'
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
      receivables: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          debtor_name: string
          debtor_contact: string | null
          description: string | null
          principal_amount: number
          current_balance: number
          interest_rate: number | null
          due_date: string | null
          status: 'active' | 'paid' | 'written_off'
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          debtor_name: string
          debtor_contact?: string | null
          description?: string | null
          principal_amount: number
          current_balance: number
          interest_rate?: number | null
          due_date?: string | null
          status?: 'active' | 'paid' | 'written_off'
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          debtor_name?: string
          debtor_contact?: string | null
          description?: string | null
          principal_amount?: number
          current_balance?: number
          interest_rate?: number | null
          due_date?: string | null
          status?: 'active' | 'paid' | 'written_off'
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

// Convenience types
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Category = Database['public']['Tables']['categories']['Row']
export type Asset = Database['public']['Tables']['assets']['Row']
export type Liability = Database['public']['Tables']['liabilities']['Row']
export type Receivable = Database['public']['Tables']['receivables']['Row']
export type Transaction = Database['public']['Tables']['transactions']['Row']
