import { createClient } from '@/lib/supabase/client'
import type { Liability, Database } from '@/lib/types/database'

type LiabilityInsert = Database['public']['Tables']['liabilities']['Insert']
type LiabilityUpdate = Database['public']['Tables']['liabilities']['Update']

export async function getLiabilities() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function getLiability(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .eq('id', id)
    .single()

  return { data, error }
}

export async function createLiability(liability: LiabilityInsert) {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return { data: null, error: authError || new Error('User not authenticated') }
  }

  const { data, error } = await supabase
    .from('liabilities')
    .insert({
      ...liability,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateLiability(id: string, updates: LiabilityUpdate) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteLiability(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('liabilities')
    .delete()
    .eq('id', id)

  return { error }
}

export async function getLiabilityCategories() {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('type', 'liability')
    .order('name')

  return { data, error }
}

export async function getRecurringLiabilities() {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('liabilities')
    .select('*')
    .eq('is_recurring', true)
    .eq('status', 'active')
    .order('next_due_date', { ascending: true })

  return { data, error }
}

export async function markRecurringPaymentPaid(liabilityId: string) {
  const supabase = createClient()

  // First get the current liability
  const { data: liability, error: fetchError } = await supabase
    .from('liabilities')
    .select('*')
    .eq('id', liabilityId)
    .single()

  if (fetchError || !liability) {
    return { data: null, error: fetchError || new Error('Liability not found') }
  }

  // Calculate the next payment details
  const updatedLiability = updateRecurringPaymentAfterPayment(liability)

  // Update the liability with new payment information
  const { data, error } = await supabase
    .from('liabilities')
    .update({
      last_payment_date: updatedLiability.last_payment_date,
      next_due_date: updatedLiability.next_due_date,
      updated_at: new Date().toISOString()
    })
    .eq('id', liabilityId)
    .select()
    .single()

  return { data, error }
}

// Utility functions for loan calculations
export function calculateMonthlyPayment(
  principal: number,
  annualRate: number,
  termInMonths: number
): number {
  if (annualRate === 0) return principal / termInMonths
  
  const monthlyRate = annualRate / 100 / 12
  const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) / 
                  (Math.pow(1 + monthlyRate, termInMonths) - 1)
  
  return payment
}

export function calculateInterestAccrued(
  principal: number,
  annualRate: number,
  daysElapsed: number
): number {
  const dailyRate = annualRate / 100 / 365
  return principal * dailyRate * daysElapsed
}

// Recurring payment utility functions
export function calculateNextDueDate(
  currentDate: Date,
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
): Date {
  const nextDate = new Date(currentDate)

  switch (frequency) {
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + 7)
      break
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1)
      break
    case 'quarterly':
      nextDate.setMonth(nextDate.getMonth() + 3)
      break
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + 1)
      break
  }

  return nextDate
}

export function getRecurringPaymentsDue(liabilities: any[], daysAhead: number = 7): any[] {
  const today = new Date()
  const checkDate = new Date()
  checkDate.setDate(today.getDate() + daysAhead)

  return liabilities.filter(liability => {
    if (!liability.is_recurring || !liability.next_due_date) return false

    const dueDate = new Date(liability.next_due_date)
    return dueDate >= today && dueDate <= checkDate
  })
}

export function isPaymentOverdue(liability: any): boolean {
  if (!liability.next_due_date) return false

  const today = new Date()
  const dueDate = new Date(liability.next_due_date)

  return dueDate < today
}

export function getUpcomingPayments(liabilities: any[], days: number = 30): any[] {
  const today = new Date()
  const endDate = new Date()
  endDate.setDate(today.getDate() + days)

  const upcomingPayments: any[] = []

  liabilities.forEach(liability => {
    if (!liability.is_recurring || !liability.recurring_frequency || !liability.next_due_date) return

    let currentDate = new Date(liability.next_due_date)

    // Generate upcoming payments within the specified period
    while (currentDate <= endDate) {
      if (currentDate >= today) {
        upcomingPayments.push({
          ...liability,
          payment_date: new Date(currentDate),
          amount: liability.recurring_amount
        })
      }

      currentDate = calculateNextDueDate(currentDate, liability.recurring_frequency)

      // Prevent infinite loop - max 100 payments
      if (upcomingPayments.length > 100) break
    }
  })

  return upcomingPayments.sort((a, b) => a.payment_date.getTime() - b.payment_date.getTime())
}

export function updateRecurringPaymentAfterPayment(liability: any): any {
  if (!liability.is_recurring || !liability.recurring_frequency || !liability.next_due_date) {
    return liability
  }

  const currentDueDate = new Date(liability.next_due_date)
  const nextDueDate = calculateNextDueDate(currentDueDate, liability.recurring_frequency)

  return {
    ...liability,
    last_payment_date: currentDueDate.toISOString().split('T')[0],
    next_due_date: nextDueDate.toISOString().split('T')[0],
    // For recurring payments, current_balance might stay the same or be updated based on payment
    updated_at: new Date().toISOString()
  }
}
