import { createClient } from '@/lib/supabase/client'
import type { Liability, Database } from '@/lib/types/database'

type LiabilityInsert = Database['public']['Tables']['liabilities']['Insert']
type LiabilityUpdate = Database['public']['Tables']['liabilities']['Update']

export async function getLiabilities() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function getLiability(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .eq('id', id)
    .single()

  return { data, error }
}

export async function createLiability(liability: LiabilityInsert) {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return { data: null, error: authError || new Error('User not authenticated') }
  }

  const { data, error } = await supabase
    .from('liabilities')
    .insert({
      ...liability,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateLiability(id: string, updates: LiabilityUpdate) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('liabilities')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteLiability(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('liabilities')
    .delete()
    .eq('id', id)

  return { error }
}

export async function getLiabilityCategories() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('type', 'liability')
    .order('name')

  return { data, error }
}

// Utility functions for loan calculations
export function calculateMonthlyPayment(
  principal: number,
  annualRate: number,
  termInMonths: number
): number {
  if (annualRate === 0) return principal / termInMonths
  
  const monthlyRate = annualRate / 100 / 12
  const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) / 
                  (Math.pow(1 + monthlyRate, termInMonths) - 1)
  
  return payment
}

export function calculateInterestAccrued(
  principal: number,
  annualRate: number,
  daysElapsed: number
): number {
  const dailyRate = annualRate / 100 / 365
  return principal * dailyRate * daysElapsed
}
