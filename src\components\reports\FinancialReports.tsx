'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { BarChart3, TrendingUp, TrendingDown, Calendar, Download, Filter } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Select } from '@/components/ui/Select'
import { Input } from '@/components/ui/Input'
import { formatCurrency, formatDate } from '@/lib/utils'
import { getTransactions } from '@/lib/api/transactions'
import { getAssets } from '@/lib/api/assets'
import { getLiabilities } from '@/lib/api/liabilities'
import { getReceivables } from '@/lib/api/receivables'
import { getCategories } from '@/lib/api/categories'

interface ReportData {
  period: string
  income: number
  expenses: number
  netIncome: number
  assets: number
  liabilities: number
  netWorth: number
}

interface CategoryBreakdown {
  category: string
  amount: number
  percentage: number
  color: string
}

export default function FinancialReports() {
  const [loading, setLoading] = useState(true)
  const [reportPeriod, setReportPeriod] = useState('monthly')
  const [reportData, setReportData] = useState<ReportData[]>([])
  const [incomeBreakdown, setIncomeBreakdown] = useState<CategoryBreakdown[]>([])
  const [expenseBreakdown, setExpenseBreakdown] = useState<CategoryBreakdown[]>([])
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })

  useEffect(() => {
    loadReportData()
  }, [reportPeriod, dateRange])

  const loadReportData = async () => {
    setLoading(true)

    try {
      // Fetch all financial data
      const [transactionsResult, assetsResult, liabilitiesResult, receivablesResult, categoriesResult] = await Promise.all([
        getTransactions(),
        getAssets(),
        getLiabilities(),
        getReceivables(),
        getCategories()
      ])

      const transactions = transactionsResult.data || []
      const assets = assetsResult.data || []
      const liabilities = liabilitiesResult.data || []
      const receivables = receivablesResult.data || []
      const categories = categoriesResult.data || []

      // Create category lookup map
      const categoryMap = categories.reduce((acc, cat) => {
        acc[cat.id] = cat.name
        return acc
      }, {} as Record<string, string>)

      // Filter transactions based on date range
      const startDate = new Date(dateRange.start)
      const endDate = new Date(dateRange.end)
      endDate.setHours(23, 59, 59, 999) // Include the entire end date

      const periodTransactions = transactions.filter(t => {
        const transactionDate = new Date(t.transaction_date)
        return transactionDate >= startDate && transactionDate <= endDate
      })

      // Calculate totals
      const income = periodTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)

      const expenses = periodTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)

      const totalAssets = assets.reduce((sum, a) => sum + a.current_value, 0)
      const totalLiabilities = liabilities.reduce((sum, l) => sum + l.current_balance, 0)
      const totalReceivables = receivables.reduce((sum, r) => sum + r.current_balance, 0)

      const netIncome = income - expenses
      const netWorth = totalAssets + totalReceivables - totalLiabilities

      // Create report data for current period
      const currentPeriodData: ReportData = {
        period: `${formatDate(dateRange.start)} - ${formatDate(dateRange.end)}`,
        income,
        expenses,
        netIncome,
        assets: totalAssets,
        liabilities: totalLiabilities,
        netWorth
      }

      setReportData([currentPeriodData])

      // Calculate category breakdowns
      const incomeByCategory = periodTransactions
        .filter(t => t.type === 'income')
        .reduce((acc, t) => {
          const categoryName = t.category_id ? categoryMap[t.category_id] || 'Unknown Category' : 'Uncategorized'
          acc[categoryName] = (acc[categoryName] || 0) + t.amount
          return acc
        }, {} as Record<string, number>)

      const expenseByCategory = periodTransactions
        .filter(t => t.type === 'expense')
        .reduce((acc, t) => {
          const categoryName = t.category_id ? categoryMap[t.category_id] || 'Unknown Category' : 'Uncategorized'
          acc[categoryName] = (acc[categoryName] || 0) + t.amount
          return acc
        }, {} as Record<string, number>)

      // Convert to breakdown format
      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316']

      const incomeBreakdownData = Object.entries(incomeByCategory).map(([categoryName, amount], index) => ({
        category: categoryName,
        amount,
        percentage: income > 0 ? Math.round((amount / income) * 100) : 0,
        color: colors[index % colors.length]
      }))

      const expenseBreakdownData = Object.entries(expenseByCategory).map(([categoryName, amount], index) => ({
        category: categoryName,
        amount,
        percentage: expenses > 0 ? Math.round((amount / expenses) * 100) : 0,
        color: colors[index % colors.length]
      }))

      setIncomeBreakdown(incomeBreakdownData)
      setExpenseBreakdown(expenseBreakdownData)

    } catch (error) {
      console.error('Error loading report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getCurrentPeriodData = () => {
    return reportData[reportData.length - 1] || {
      period: '',
      income: 0,
      expenses: 0,
      netIncome: 0,
      assets: 0,
      liabilities: 0,
      netWorth: 0
    }
  }

  const getPreviousPeriodData = () => {
    return reportData[reportData.length - 2] || getCurrentPeriodData()
  }

  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }

  const exportReport = () => {
    // Export functionality
    console.log('Exporting report...')
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="h-32 bg-gray-300 rounded"></div>
            <div className="h-32 bg-gray-300 rounded"></div>
            <div className="h-32 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  const currentData = getCurrentPeriodData()
  const previousData = getPreviousPeriodData()

  return (
    <div className="space-y-8">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Financial Reports
          </h1>
          <p className="text-gray-600 mt-2">Analyze your financial performance and trends</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
            <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
              <label className="text-sm font-medium whitespace-nowrap">From:</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
              <label className="text-sm font-medium whitespace-nowrap">To:</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select
              value={reportPeriod}
              onChange={(e) => {
                setReportPeriod(e.target.value)
                // Auto-set date ranges based on period
                const now = new Date()
                if (e.target.value === 'weekly') {
                  const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
                  const weekEnd = new Date(now.setDate(weekStart.getDate() + 6))
                  setDateRange({
                    start: weekStart.toISOString().split('T')[0],
                    end: weekEnd.toISOString().split('T')[0]
                  })
                } else if (e.target.value === 'monthly') {
                  setDateRange({
                    start: new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0],
                    end: new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0]
                  })
                }
              }}
              className="w-full sm:w-auto"
            >
              <option value="custom">Custom Range</option>
              <option value="weekly">This Week</option>
              <option value="monthly">This Month</option>
              <option value="quarterly">This Quarter</option>
              <option value="yearly">This Year</option>
            </Select>
            <Button onClick={exportReport} variant="outline" className="w-full sm:w-auto">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Net Income</p>
                <p className="text-2xl font-bold text-green-700">
                  {formatCurrency(currentData.netIncome, 'LKR')}
                </p>
                <div className="flex items-center mt-2">
                  {calculateGrowth(currentData.netIncome, previousData.netIncome) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm ${
                    calculateGrowth(currentData.netIncome, previousData.netIncome) >= 0 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {Math.abs(calculateGrowth(currentData.netIncome, previousData.netIncome)).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-green-200 rounded-xl">
                <BarChart3 className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Net Worth</p>
                <p className="text-2xl font-bold text-blue-700">
                  {formatCurrency(currentData.netWorth, 'LKR')}
                </p>
                <div className="flex items-center mt-2">
                  {calculateGrowth(currentData.netWorth, previousData.netWorth) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-blue-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm ${
                    calculateGrowth(currentData.netWorth, previousData.netWorth) >= 0 
                      ? 'text-blue-600' 
                      : 'text-red-600'
                  }`}>
                    {Math.abs(calculateGrowth(currentData.netWorth, previousData.netWorth)).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-blue-200 rounded-xl">
                <TrendingUp className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Savings Rate</p>
                <p className="text-2xl font-bold text-purple-700">
                  {currentData.income > 0 ? ((currentData.netIncome / currentData.income) * 100).toFixed(1) : 0}%
                </p>
                <div className="flex items-center mt-2">
                  <Calendar className="h-4 w-4 text-purple-600 mr-1" />
                  <span className="text-sm text-purple-600">This period</span>
                </div>
              </div>
              <div className="p-3 bg-purple-200 rounded-xl">
                <Calendar className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Income vs Expenses Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Income vs Expenses Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.map((data, index) => (
              <motion.div
                key={data.period}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 border rounded-xl"
              >
                <div className="flex-1">
                  <h3 className="font-semibold">{data.period}</h3>
                  <div className="flex space-x-6 mt-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm">Income: {formatCurrency(data.income, 'LKR')}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-sm">Expenses: {formatCurrency(data.expenses, 'LKR')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-lg">
                    {formatCurrency(data.netIncome, 'LKR')}
                  </p>
                  <p className="text-sm text-gray-500">Net Income</p>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Income Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {incomeBreakdown.map((item, index) => (
                <motion.div
                  key={item.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full mr-3"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="font-medium">{item.category}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(item.amount, 'LKR')}</p>
                    <p className="text-sm text-gray-500">{item.percentage}%</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Expense Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expenseBreakdown.map((item, index) => (
                <motion.div
                  key={item.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full mr-3"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="font-medium">{item.category}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(item.amount, 'LKR')}</p>
                    <p className="text-sm text-gray-500">{item.percentage}%</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
