'use client'

import { useState, useEffect } from 'react'
import { Plus, Receipt, Target, BarChart3, TrendingUp } from 'lucide-react'
import { getDashboardData, type DashboardData } from '@/lib/api/dashboard'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import NetWorthCard from './NetWorthCard'
import QuickStats from './QuickStats'
import Asset<PERSON>hart from './AssetChart'
import RecurringPaymentsCard from './RecurringPaymentsCard'

export default function Dashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      const dashboardData = await getDashboardData()
      setData(dashboardData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-red-600">
        Error loading dashboard data. Please try again.
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
          Dashboard
        </h1>
        <p className="text-gray-600 mt-2">Overview of your financial portfolio</p>
      </div>

      <QuickStats
        totalAssets={data.totalAssets}
        totalLiabilities={data.totalLiabilities}
        totalReceivables={data.totalReceivables}
        overdueItems={data.overdueItems}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <NetWorthCard
          totalAssets={data.totalAssets}
          totalLiabilities={data.totalLiabilities}
          totalReceivables={data.totalReceivables}
          netWorth={data.netWorth}
        />

        <AssetChart data={data.assetsByType} />
      </div>

      {/* Recurring Payments Section */}
      <RecurringPaymentsCard
        monthlyTotal={data.recurringPayments.monthlyTotal}
        upcomingPayments={data.recurringPayments.upcomingPayments}
        overduePayments={data.recurringPayments.overduePayments}
        hasRecurringPayments={data.recurringPayments.hasRecurringPayments}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
            <Button
              variant="outline"
              className="h-16 lg:h-20 flex-col hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
              onClick={() => window.location.href = '/transactions'}
            >
              <Receipt className="h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-blue-600" />
              <span className="text-xs lg:text-sm font-medium">Add Transaction</span>
            </Button>
            <Button
              variant="outline"
              className="h-16 lg:h-20 flex-col hover:bg-green-50 hover:border-green-300 transition-all duration-200"
              onClick={() => window.location.href = '/budget'}
            >
              <Target className="h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-green-600" />
              <span className="text-xs lg:text-sm font-medium">Budget Planner</span>
            </Button>
            <Button
              variant="outline"
              className="h-16 lg:h-20 flex-col hover:bg-purple-50 hover:border-purple-300 transition-all duration-200"
              onClick={() => window.location.href = '/assets'}
            >
              <Plus className="h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-purple-600" />
              <span className="text-xs lg:text-sm font-medium">Add Asset</span>
            </Button>
            <Button
              variant="outline"
              className="h-16 lg:h-20 flex-col hover:bg-orange-50 hover:border-orange-300 transition-all duration-200"
              onClick={() => window.location.href = '/reports'}
            >
              <BarChart3 className="h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-orange-600" />
              <span className="text-xs lg:text-sm font-medium">View Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      {data.recentTransactions && data.recentTransactions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Recent Transactions
              <Button variant="outline" size="sm" onClick={() => window.location.href = '/transactions'}>
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recentTransactions.slice(0, 5).map((transaction: any) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-full mr-3 ${
                      transaction.type === 'income' ? 'bg-green-100 text-green-600' :
                      transaction.type === 'expense' ? 'bg-red-100 text-red-600' :
                      'bg-blue-100 text-blue-600'
                    }`}>
                      {transaction.type === 'income' ? <TrendingUp className="h-4 w-4" /> :
                       transaction.type === 'expense' ? <TrendingUp className="h-4 w-4 rotate-180" /> :
                       <Receipt className="h-4 w-4" />}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description || 'No description'}</p>
                      <p className="text-sm text-gray-500">
                        {transaction.categories?.name || 'Uncategorized'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.type === 'income' ? '+' : '-'}
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: transaction.currency || 'LKR',
                        currencyDisplay: 'symbol'
                      }).format(transaction.amount).replace('LKR', 'Rs.')}
                    </p>
                    <p className="text-sm text-gray-500">
                      {new Date(transaction.transaction_date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      {(data.overdueItems.liabilities > 0 ||
        data.overdueItems.receivables > 0 ||
        data.recurringPayments.overduePayments.length > 0) ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">⚠️ Attention Required</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-red-700 space-y-1">
              {data.overdueItems.liabilities > 0 && (
                <p>• {data.overdueItems.liabilities} overdue liability(ies) need attention</p>
              )}
              {data.overdueItems.receivables > 0 && (
                <p>• {data.overdueItems.receivables} overdue receivable(s) need follow-up</p>
              )}
              {data.recurringPayments.overduePayments.length > 0 && (
                <p>• {data.recurringPayments.overduePayments.length} overdue recurring payment(s) need to be paid</p>
              )}
            </div>
          </CardContent>
        </Card>
      ) : null}
    </div>
  )
}
