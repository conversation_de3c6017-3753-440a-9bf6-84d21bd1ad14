'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createLiability, updateLiability } from '@/lib/api/liabilities'
import type { Liability, RecurringFrequency } from '@/lib/types/database'
import { Calendar, CreditCard, Repeat, AlertCircle, Info } from 'lucide-react'
import { validateRecurringPaymentData } from '@/lib/utils/recurringPayments'
import {
  getFieldRequirements,
  getLiabilityTypeConfig,
  validateLiabilityByType,
  shouldDefaultToRecurring,
  getCommonFrequencies
} from '@/lib/utils/liabilityTypes'

interface LiabilityFormProps {
  liability?: Liability
  onSuccess: () => void
  onCancel: () => void
}

export default function LiabilityForm({ liability, onSuccess, onCancel }: LiabilityFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: liability?.name || '',
    description: liability?.description || '',
    principal_amount: liability?.principal_amount || 0,
    current_balance: liability?.current_balance || 0,
    interest_rate: liability?.interest_rate || 0,
    due_date: liability?.due_date || '',
    liability_type: liability?.liability_type || 'other' as const, // Default to 'other' for flexibility
    status: liability?.status || 'active' as const,
    currency: liability?.currency || 'LKR',
    // Recurring payment fields
    is_recurring: liability?.is_recurring || false,
    recurring_frequency: liability?.recurring_frequency || null as RecurringFrequency | null,
    recurring_amount: liability?.recurring_amount || 0,
    next_due_date: liability?.next_due_date || '',
    last_payment_date: liability?.last_payment_date || '',
    recurring_start_date: liability?.recurring_start_date || '',
    recurring_end_date: liability?.recurring_end_date || '',
    auto_deduct: liability?.auto_deduct || false,
    payment_method: liability?.payment_method || '',
    reminder_days: liability?.reminder_days || 3,
  })

  const [interestRateType, setInterestRateType] = useState<'annual' | 'monthly' | 'daily'>('annual')

  // Get field requirements based on liability type
  const fieldRequirements = getFieldRequirements(formData.liability_type)
  const typeConfig = getLiabilityTypeConfig(formData.liability_type)

  useEffect(() => {
    // Auto-set current balance to principal amount for new liabilities (only for debt types)
    if (!liability && formData.principal_amount > 0 && formData.current_balance === 0 && fieldRequirements.currentBalance !== 'hidden') {
      setFormData(prev => ({ ...prev, current_balance: prev.principal_amount }))
    }
  }, [formData.principal_amount, liability, fieldRequirements.currentBalance])

  useEffect(() => {
    // Auto-enable recurring for recurring payment types
    if (!liability && shouldDefaultToRecurring(formData.liability_type) && !formData.is_recurring) {
      setFormData(prev => ({
        ...prev,
        is_recurring: true,
        recurring_frequency: 'monthly', // Default to monthly
        recurring_amount: 0,
        recurring_start_date: new Date().toISOString().split('T')[0]
      }))
    }
  }, [formData.liability_type, liability])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate liability data based on type requirements
      const typeValidationErrors = validateLiabilityByType(formData)
      if (typeValidationErrors.length > 0) {
        alert('Validation errors:\n' + typeValidationErrors.join('\n'))
        setLoading(false)
        return
      }

      // Validate recurring payment data
      if (formData.is_recurring) {
        const recurringValidationErrors = validateRecurringPaymentData(formData)
        if (recurringValidationErrors.length > 0) {
          alert('Recurring payment validation errors:\n' + recurringValidationErrors.join('\n'))
          setLoading(false)
          return
        }
      }

      // Helper function to convert empty strings to null for date fields
      const formatDateField = (dateString: string) => {
        return dateString && dateString.trim() !== '' ? dateString : null
      }

      // Prepare liability data with proper date formatting
      const liabilityData = {
        ...formData,
        due_date: formatDateField(formData.due_date),
        next_due_date: formatDateField(formData.next_due_date),
        last_payment_date: formatDateField(formData.last_payment_date),
        recurring_start_date: formatDateField(formData.recurring_start_date),
        recurring_end_date: formatDateField(formData.recurring_end_date),
      }

      let result
      if (liability) {
        result = await updateLiability(liability.id, liabilityData)
      } else {
        result = await createLiability(liabilityData)
      }

      if (result.error) {
        console.error('Error saving liability:', result.error)
        alert('Error saving liability: ' + result.error.message)
        return
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving liability:', error)
      alert('Error saving liability: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    // Validate numeric fields to prevent overflow
    if ((field === 'principal_amount' || field === 'current_balance' || field === 'interest_rate' || field === 'recurring_amount') && typeof value === 'number') {
      // Limit to reasonable values to prevent database overflow
      if (value > 999999999999) {
        alert('Value is too large. Please enter a smaller amount.')
        return
      }
    }

    // Handle recurring payment toggle
    if (field === 'is_recurring') {
      const newFormData = { ...formData, [field]: value }

      // If turning off recurring, clear recurring fields
      if (!value) {
        newFormData.recurring_frequency = null
        newFormData.recurring_amount = 0
        newFormData.next_due_date = ''
        newFormData.recurring_start_date = ''
        newFormData.recurring_end_date = ''
        newFormData.auto_deduct = false
        newFormData.payment_method = ''
        newFormData.reminder_days = 3
      } else {
        // If turning on recurring, set some defaults
        newFormData.recurring_amount = formData.current_balance
        newFormData.recurring_start_date = new Date().toISOString().split('T')[0]
        newFormData.next_due_date = formData.due_date || new Date().toISOString().split('T')[0]
      }

      setFormData(newFormData)
      return
    }

    // Handle liability type change - reset form appropriately
    if (field === 'liability_type') {
      const newTypeConfig = getLiabilityTypeConfig(value)
      const newFieldRequirements = getFieldRequirements(value)

      const updatedFormData = { ...formData, [field]: value }

      // Reset fields that are not applicable to the new type
      if (newFieldRequirements.principal === 'hidden') {
        updatedFormData.principal_amount = 0
      }
      if (newFieldRequirements.currentBalance === 'hidden') {
        updatedFormData.current_balance = 0
      }

      // Auto-enable recurring for recurring payment types
      if (newTypeConfig.defaultRecurring && !formData.is_recurring) {
        updatedFormData.is_recurring = true
        updatedFormData.recurring_frequency = newTypeConfig.commonFrequencies[0] || 'monthly'
        updatedFormData.recurring_amount = 0
        updatedFormData.recurring_start_date = new Date().toISOString().split('T')[0]
      }

      setFormData(updatedFormData)
      return
    }

    // Handle recurring frequency change - auto-calculate next due date
    if (field === 'recurring_frequency' && value && formData.recurring_start_date) {
      const startDate = new Date(formData.recurring_start_date)
      let nextDate = new Date(startDate)

      switch (value) {
        case 'weekly':
          nextDate.setDate(nextDate.getDate() + 7)
          break
        case 'monthly':
          nextDate.setMonth(nextDate.getMonth() + 1)
          break
        case 'quarterly':
          nextDate.setMonth(nextDate.getMonth() + 3)
          break
        case 'yearly':
          nextDate.setFullYear(nextDate.getFullYear() + 1)
          break
      }

      setFormData(prev => ({
        ...prev,
        [field]: value,
        next_due_date: nextDate.toISOString().split('T')[0]
      }))
      return
    }

    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{liability ? 'Edit Liability' : 'Add New Liability'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Liability Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="liability_type">Type *</Label>
              <Select
                id="liability_type"
                value={formData.liability_type}
                onChange={(e) => handleChange('liability_type', e.target.value)}
                required
              >
                <option value="loan_taken">Loan Taken</option>
                <option value="credit_card">Credit Card</option>
                <option value="mortgage">Mortgage</option>
                <option value="insurance">Insurance</option>
                <option value="utilities">Utilities</option>
                <option value="taxes">Taxes</option>
                <option value="subscription">Subscription</option>
                <option value="rent">Rent</option>
                <option value="medical">Medical</option>
                <option value="education">Education</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
            />
          </div>

          {/* Type-specific information */}
          {typeConfig.description && (
            <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm text-blue-800 font-medium">{typeConfig.label}</p>
                  <p className="text-xs text-blue-600">{typeConfig.description}</p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Principal Amount - Conditional */}
            {fieldRequirements.principal !== 'hidden' && (
              <div>
                <Label htmlFor="principal_amount">
                  Principal Amount {fieldRequirements.principal === 'required' ? '*' : '(Optional)'}
                </Label>
                <Input
                  id="principal_amount"
                  type="number"
                  step="0.01"
                  value={formData.principal_amount || ''}
                  onChange={(e) => handleChange('principal_amount', parseFloat(e.target.value) || 0)}
                  required={fieldRequirements.principal === 'required'}
                  placeholder="0.00"
                />
              </div>
            )}

            {/* Current Balance - Conditional */}
            {fieldRequirements.currentBalance !== 'hidden' && (
              <div>
                <Label htmlFor="current_balance">
                  Current Balance {fieldRequirements.currentBalance === 'required' ? '*' : '(Optional)'}
                </Label>
                <Input
                  id="current_balance"
                  type="number"
                  step="0.01"
                  value={formData.current_balance || ''}
                  onChange={(e) => handleChange('current_balance', parseFloat(e.target.value) || 0)}
                  required={fieldRequirements.currentBalance === 'required'}
                  placeholder="0.00"
                />
              </div>
            )}
            
            <div>
              <Label htmlFor="interest_rate">Interest Rate (%) - Optional</Label>
              <div className="flex gap-2">
                <Input
                  id="interest_rate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.interest_rate || ''}
                  onChange={(e) => handleChange('interest_rate', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="flex-1"
                />
                <Select
                  value={interestRateType}
                  onChange={(e) => setInterestRateType(e.target.value as 'annual' | 'monthly' | 'daily')}
                  className="w-32"
                >
                  <option value="annual">Annual</option>
                  <option value="monthly">Monthly</option>
                  <option value="daily">Daily</option>
                </Select>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Due Date - Conditional */}
            {!formData.is_recurring && (
              <div>
                <Label htmlFor="due_date">
                  Due Date {fieldRequirements.dueDate === 'required' ? '*' : '(Optional)'}
                </Label>
                <Input
                  id="due_date"
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => handleChange('due_date', e.target.value)}
                  required={fieldRequirements.dueDate === 'required'}
                />
                {fieldRequirements.dueDate === 'required' && (
                  <p className="text-xs text-gray-500 mt-1">
                    Required for {typeConfig.label} liabilities
                  </p>
                )}
              </div>
            )}
            
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                id="status"
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
              >
                <option value="active">Active</option>
                <option value="paid_off">Paid Off</option>
                <option value="defaulted">Defaulted</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
              >
                <option value="LKR">LKR (Sri Lankan Rupee)</option>
                <option value="USD">USD (US Dollar)</option>
              </Select>
            </div>
          </div>

          {/* Recurring Payment Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center space-x-2 mb-4">
              <Repeat className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">Recurring Payment Settings</h3>
            </div>

            <div className="space-y-4">
              {/* Recurring Toggle */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="is_recurring"
                  checked={formData.is_recurring}
                  onChange={(e) => handleChange('is_recurring', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="is_recurring" className="text-sm font-medium text-gray-700">
                  This is a recurring payment (e.g., monthly rent, insurance premium)
                </Label>
              </div>

              {/* Recurring Payment Fields - Only show when recurring is enabled */}
              {formData.is_recurring && (
                <div className="bg-blue-50 p-4 rounded-lg space-y-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-700">Configure your recurring payment schedule</span>
                    {fieldRequirements.recurringRecommended && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        Recommended for {typeConfig.label}
                      </span>
                    )}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recurring_frequency">Payment Frequency *</Label>
                      <Select
                        id="recurring_frequency"
                        value={formData.recurring_frequency || ''}
                        onChange={(e) => handleChange('recurring_frequency', e.target.value as RecurringFrequency)}
                        required={formData.is_recurring}
                      >
                        <option value="">Select frequency</option>
                        {getCommonFrequencies(formData.liability_type).map(freq => (
                          <option key={freq} value={freq}>
                            {freq.charAt(0).toUpperCase() + freq.slice(1)}
                            {freq === 'quarterly' && ' (3 months)'}
                          </option>
                        ))}
                        {/* Show all options if not in common frequencies */}
                        {!getCommonFrequencies(formData.liability_type).includes('weekly') && (
                          <option value="weekly">Weekly</option>
                        )}
                        {!getCommonFrequencies(formData.liability_type).includes('monthly') && (
                          <option value="monthly">Monthly</option>
                        )}
                        {!getCommonFrequencies(formData.liability_type).includes('quarterly') && (
                          <option value="quarterly">Quarterly (3 months)</option>
                        )}
                        {!getCommonFrequencies(formData.liability_type).includes('yearly') && (
                          <option value="yearly">Yearly</option>
                        )}
                      </Select>
                      {getCommonFrequencies(formData.liability_type).length > 0 && (
                        <p className="text-xs text-gray-500 mt-1">
                          Common for {typeConfig.label}: {getCommonFrequencies(formData.liability_type).join(', ')}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="recurring_amount">Payment Amount *</Label>
                      <Input
                        id="recurring_amount"
                        type="number"
                        step="0.01"
                        value={formData.recurring_amount || ''}
                        onChange={(e) => handleChange('recurring_amount', parseFloat(e.target.value) || 0)}
                        required={formData.is_recurring}
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recurring_start_date">Start Date *</Label>
                      <Input
                        id="recurring_start_date"
                        type="date"
                        value={formData.recurring_start_date}
                        onChange={(e) => handleChange('recurring_start_date', e.target.value)}
                        required={formData.is_recurring}
                      />
                    </div>

                    <div>
                      <Label htmlFor="next_due_date">Next Due Date</Label>
                      <Input
                        id="next_due_date"
                        type="date"
                        value={formData.next_due_date}
                        onChange={(e) => handleChange('next_due_date', e.target.value)}
                        className="bg-gray-50"
                        title="Auto-calculated based on frequency and start date"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recurring_end_date">End Date (Optional)</Label>
                      <Input
                        id="recurring_end_date"
                        type="date"
                        value={formData.recurring_end_date}
                        onChange={(e) => handleChange('recurring_end_date', e.target.value)}
                        placeholder="Leave empty for ongoing"
                      />
                    </div>

                    <div>
                      <Label htmlFor="reminder_days">Reminder Days Before Due</Label>
                      <Select
                        id="reminder_days"
                        value={formData.reminder_days.toString()}
                        onChange={(e) => handleChange('reminder_days', parseInt(e.target.value))}
                      >
                        <option value="1">1 day</option>
                        <option value="3">3 days</option>
                        <option value="7">1 week</option>
                        <option value="14">2 weeks</option>
                        <option value="30">1 month</option>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="payment_method">Payment Method (Optional)</Label>
                      <Input
                        id="payment_method"
                        value={formData.payment_method}
                        onChange={(e) => handleChange('payment_method', e.target.value)}
                        placeholder="e.g., Bank Auto-debit, Credit Card, Cash"
                      />
                    </div>

                    <div className="flex items-center space-x-3 pt-6">
                      <input
                        type="checkbox"
                        id="auto_deduct"
                        checked={formData.auto_deduct}
                        onChange={(e) => handleChange('auto_deduct', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <Label htmlFor="auto_deduct" className="text-sm">
                        Auto-deduct enabled
                      </Label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : liability ? 'Update Liability' : 'Create Liability'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
