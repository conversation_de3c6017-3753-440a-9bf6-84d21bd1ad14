import type { Liability } from '@/lib/types/database'

export type LiabilityCategory = 'debt' | 'recurring_payment' | 'mixed'

export interface LiabilityTypeConfig {
  category: LiabilityCategory
  label: string
  description: string
  requiresPrincipal: boolean
  requiresCurrentBalance: boolean
  requiresDueDate: boolean
  defaultRecurring: boolean
  commonFrequencies: ('weekly' | 'monthly' | 'quarterly' | 'yearly')[]
}

/**
 * Configuration for each liability type
 */
export const LIABILITY_TYPE_CONFIG: Record<string, LiabilityTypeConfig> = {
  // DEBT-BASED LIABILITIES (Traditional loans and debts)
  loan_taken: {
    category: 'debt',
    label: 'Loan Taken',
    description: 'Personal loans, business loans, or other borrowed money',
    requiresPrincipal: true,
    requiresCurrentBalance: true,
    requiresDueDate: false, // Can have payment schedule or be open-ended
    defaultRecurring: false,
    commonFrequencies: ['monthly', 'quarterly']
  },
  
  credit_card: {
    category: 'debt',
    label: 'Credit Card',
    description: 'Credit card debt and outstanding balances',
    requiresPrincipal: false, // Credit cards don't have a fixed principal
    requiresCurrentBalance: true,
    requiresDueDate: false, // Monthly statements, not fixed due date
    defaultRecurring: false,
    commonFrequencies: ['monthly']
  },
  
  mortgage: {
    category: 'debt',
    label: 'Mortgage',
    description: 'Home mortgage or property loans',
    requiresPrincipal: true,
    requiresCurrentBalance: true,
    requiresDueDate: false, // Long-term with payment schedule
    defaultRecurring: true, // Mortgages are typically recurring
    commonFrequencies: ['monthly']
  },
  
  education: {
    category: 'debt',
    label: 'Education',
    description: 'Student loans and education-related debt',
    requiresPrincipal: true,
    requiresCurrentBalance: true,
    requiresDueDate: false,
    defaultRecurring: false,
    commonFrequencies: ['monthly', 'quarterly']
  },
  
  medical: {
    category: 'debt',
    label: 'Medical',
    description: 'Medical bills and healthcare debt',
    requiresPrincipal: true,
    requiresCurrentBalance: true,
    requiresDueDate: true, // Medical bills usually have specific due dates
    defaultRecurring: false,
    commonFrequencies: ['monthly']
  },

  // RECURRING PAYMENT LIABILITIES (Regular expenses)
  insurance: {
    category: 'recurring_payment',
    label: 'Insurance',
    description: 'Insurance premiums (health, car, life, etc.)',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false, // Recurring payments handle due dates
    defaultRecurring: true,
    commonFrequencies: ['monthly', 'quarterly', 'yearly']
  },
  
  utilities: {
    category: 'recurring_payment',
    label: 'Utilities',
    description: 'Electricity, water, gas, internet, phone bills',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false,
    defaultRecurring: true,
    commonFrequencies: ['weekly', 'monthly']
  },
  
  taxes: {
    category: 'recurring_payment',
    label: 'Taxes',
    description: 'Tax payments and obligations',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false,
    defaultRecurring: true,
    commonFrequencies: ['monthly', 'quarterly', 'yearly']
  },
  
  subscription: {
    category: 'recurring_payment',
    label: 'Subscription',
    description: 'Netflix, Spotify, software subscriptions, etc.',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false,
    defaultRecurring: true,
    commonFrequencies: ['monthly', 'yearly']
  },
  
  rent: {
    category: 'recurring_payment',
    label: 'Rent',
    description: 'Monthly rent payments',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false,
    defaultRecurring: true,
    commonFrequencies: ['monthly']
  },

  // MIXED TYPE (Can be either)
  other: {
    category: 'mixed',
    label: 'Other',
    description: 'Other types of liabilities',
    requiresPrincipal: false,
    requiresCurrentBalance: false,
    requiresDueDate: false,
    defaultRecurring: false,
    commonFrequencies: ['weekly', 'monthly', 'quarterly', 'yearly']
  }
}

/**
 * Get configuration for a liability type
 */
export function getLiabilityTypeConfig(liabilityType: string): LiabilityTypeConfig {
  return LIABILITY_TYPE_CONFIG[liabilityType] || LIABILITY_TYPE_CONFIG.other
}

/**
 * Check if a liability type requires principal amount
 */
export function requiresPrincipalAmount(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).requiresPrincipal
}

/**
 * Check if a liability type requires current balance
 */
export function requiresCurrentBalance(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).requiresCurrentBalance
}

/**
 * Check if a liability type requires due date
 */
export function requiresDueDate(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).requiresDueDate
}

/**
 * Check if a liability type should default to recurring
 */
export function shouldDefaultToRecurring(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).defaultRecurring
}

/**
 * Get common frequencies for a liability type
 */
export function getCommonFrequencies(liabilityType: string): ('weekly' | 'monthly' | 'quarterly' | 'yearly')[] {
  return getLiabilityTypeConfig(liabilityType).commonFrequencies
}

/**
 * Get liability types by category
 */
export function getLiabilityTypesByCategory(category: LiabilityCategory): string[] {
  return Object.entries(LIABILITY_TYPE_CONFIG)
    .filter(([_, config]) => config.category === category)
    .map(([type, _]) => type)
}

/**
 * Get all debt-based liability types
 */
export function getDebtLiabilityTypes(): string[] {
  return getLiabilityTypesByCategory('debt')
}

/**
 * Get all recurring payment liability types
 */
export function getRecurringPaymentLiabilityTypes(): string[] {
  return getLiabilityTypesByCategory('recurring_payment')
}

/**
 * Check if a liability type is debt-based
 */
export function isDebtLiability(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).category === 'debt'
}

/**
 * Check if a liability type is recurring payment-based
 */
export function isRecurringPaymentLiability(liabilityType: string): boolean {
  return getLiabilityTypeConfig(liabilityType).category === 'recurring_payment'
}

/**
 * Validate liability data based on type requirements
 */
export function validateLiabilityByType(data: Partial<Liability>): string[] {
  const errors: string[] = []
  const config = getLiabilityTypeConfig(data.liability_type || '')

  // Check required fields based on liability type
  if (config.requiresPrincipal && (!data.principal_amount || data.principal_amount <= 0)) {
    errors.push(`Principal amount is required for ${config.label}`)
  }

  if (config.requiresCurrentBalance && (!data.current_balance || data.current_balance < 0)) {
    errors.push(`Current balance is required for ${config.label}`)
  }

  if (config.requiresDueDate && !data.due_date) {
    errors.push(`Due date is required for ${config.label}`)
  }

  // For recurring payment types, suggest enabling recurring if not already set
  if (config.defaultRecurring && !data.is_recurring) {
    // This is just a suggestion, not an error
    console.info(`Consider enabling recurring payments for ${config.label}`)
  }

  return errors
}

/**
 * Get field requirements summary for a liability type
 */
export function getFieldRequirements(liabilityType: string): {
  principal: 'required' | 'optional' | 'hidden'
  currentBalance: 'required' | 'optional' | 'hidden'
  dueDate: 'required' | 'optional' | 'hidden'
  recurringRecommended: boolean
} {
  const config = getLiabilityTypeConfig(liabilityType)
  
  return {
    principal: config.requiresPrincipal ? 'required' : 
               config.category === 'recurring_payment' ? 'hidden' : 'optional',
    currentBalance: config.requiresCurrentBalance ? 'required' : 
                   config.category === 'recurring_payment' ? 'hidden' : 'optional',
    dueDate: config.requiresDueDate ? 'required' : 'optional',
    recurringRecommended: config.defaultRecurring
  }
}
