import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Repeat, Calendar, AlertTriangle, CreditCard } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'
import { getFrequencyDisplayText } from '@/lib/utils/recurringPayments'
import type { RecurringPaymentAlert } from '@/lib/utils/recurringPayments'

interface RecurringPaymentsCardProps {
  monthlyTotal: number
  upcomingPayments: RecurringPaymentAlert[]
  overduePayments: RecurringPaymentAlert[]
  hasRecurringPayments?: boolean
}

export default function RecurringPaymentsCard({
  monthlyTotal,
  upcomingPayments,
  overduePayments,
  hasRecurringPayments = false
}: RecurringPaymentsCardProps) {
  const allPayments = [...overduePayments, ...upcomingPayments].slice(0, 5) // Show max 5 payments

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Repeat className="mr-2 h-5 w-5 text-blue-600" />
          Recurring Payments
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Monthly Total */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Monthly Recurring Total</p>
                <p className="text-2xl font-bold text-blue-700">
                  {formatCurrency(monthlyTotal)}
                </p>
              </div>
              <div className="p-3 bg-blue-200 rounded-xl">
                <Calendar className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </div>

          {/* Alerts */}
          {overduePayments.length > 0 && (
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">
                  {overduePayments.length} Overdue Payment{overduePayments.length > 1 ? 's' : ''}
                </span>
              </div>
              <p className="text-xs text-red-600">
                Total overdue: {formatCurrency(overduePayments.reduce((sum, p) => sum + p.amount, 0))}
              </p>
            </div>
          )}

          {/* Upcoming Payments List */}
          {allPayments.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                {overduePayments.length > 0 ? 'Overdue & Upcoming' : 'Upcoming Payments'}
              </h4>
              {allPayments.map((payment) => (
                <div
                  key={payment.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    payment.isOverdue
                      ? 'bg-red-50 border-red-200'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      payment.isOverdue ? 'bg-red-100' : 'bg-blue-100'
                    }`}>
                      {payment.isOverdue ? (
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      ) : (
                        <CreditCard className="h-4 w-4 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{payment.name}</p>
                      <p className="text-xs text-gray-500">
                        {getFrequencyDisplayText(payment.frequency)} •
                        {payment.isOverdue ? (
                          <span className="text-red-600 ml-1">
                            {Math.abs(payment.daysUntilDue)} days overdue
                          </span>
                        ) : (
                          <span className="ml-1">
                            Due {formatDate(payment.dueDate)}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold text-sm ${
                      payment.isOverdue ? 'text-red-600' : 'text-gray-900'
                    }`}>
                      {formatCurrency(payment.amount, payment.currency)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : hasRecurringPayments ? (
            <div className="text-center py-6 text-gray-500">
              <Calendar className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <p className="text-sm">No payments due in the next 7 days</p>
              <p className="text-xs text-gray-400 mt-1">
                Your recurring payments are all up to date
              </p>
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500">
              <Repeat className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">No recurring payments set up</p>
              <p className="text-xs text-gray-400 mt-1">
                Add recurring payments in the Liabilities section
              </p>
            </div>
          )}

          {/* View All Button */}
          {(allPayments.length > 0 || hasRecurringPayments) && (
            <div className="pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => window.location.href = '/liabilities'}
              >
                View All Recurring Payments
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
