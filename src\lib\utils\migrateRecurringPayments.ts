import { createClient } from '@/lib/supabase/client'
import type { Liability } from '@/lib/types/database'

/**
 * Utility to migrate existing liabilities to have proper recurring payment data
 * This should be run once to fix existing data
 */
export async function migrateRecurringPayments() {
  const supabase = createClient()

  try {
    // Get all liabilities that are marked as recurring but missing required fields
    const { data: liabilities, error: fetchError } = await supabase
      .from('liabilities')
      .select('*')
      .eq('is_recurring', true)
      .eq('status', 'active')

    if (fetchError) {
      console.error('Error fetching liabilities:', fetchError)
      return { success: false, error: fetchError }
    }

    if (!liabilities || liabilities.length === 0) {
      console.log('No recurring liabilities found to migrate')
      return { success: true, message: 'No liabilities to migrate' }
    }

    const updates: Array<{ id: string; updates: Partial<Liability> }> = []

    liabilities.forEach(liability => {
      const updateData: Partial<Liability> = {}
      let needsUpdate = false

      // Fix missing next_due_date
      if (!liability.next_due_date) {
        if (liability.due_date) {
          updateData.next_due_date = liability.due_date
        } else {
          // Set to today if no due date exists
          updateData.next_due_date = new Date().toISOString().split('T')[0]
        }
        needsUpdate = true
      }

      // Fix missing recurring_amount
      if (!liability.recurring_amount || liability.recurring_amount === 0) {
        updateData.recurring_amount = liability.current_balance
        needsUpdate = true
      }

      // Fix missing recurring_frequency
      if (!liability.recurring_frequency) {
        updateData.recurring_frequency = 'monthly' // Default to monthly
        needsUpdate = true
      }

      // Set default recurring_start_date if missing
      if (!liability.recurring_start_date) {
        updateData.recurring_start_date = liability.due_date || new Date().toISOString().split('T')[0]
        needsUpdate = true
      }

      // Set default reminder_days if missing
      if (liability.reminder_days === null || liability.reminder_days === undefined) {
        updateData.reminder_days = 3
        needsUpdate = true
      }

      if (needsUpdate) {
        updates.push({
          id: liability.id,
          updates: updateData
        })
      }
    })

    if (updates.length === 0) {
      console.log('All recurring liabilities are already properly configured')
      return { success: true, message: 'No updates needed' }
    }

    // Apply updates
    const results = await Promise.all(
      updates.map(async ({ id, updates: updateData }) => {
        const { error } = await supabase
          .from('liabilities')
          .update(updateData)
          .eq('id', id)

        return { id, success: !error, error }
      })
    )

    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length

    console.log(`Migration completed: ${successful} successful, ${failed} failed`)

    return {
      success: failed === 0,
      message: `Updated ${successful} recurring liabilities`,
      details: { successful, failed, total: updates.length }
    }

  } catch (error) {
    console.error('Migration error:', error)
    return { success: false, error }
  }
}

/**
 * Check if recurring payments need migration
 */
export async function checkRecurringPaymentsMigrationNeeded(): Promise<boolean> {
  const supabase = createClient()

  try {
    const { data: liabilities, error } = await supabase
      .from('liabilities')
      .select('id, is_recurring, next_due_date, recurring_amount, recurring_frequency')
      .eq('is_recurring', true)
      .eq('status', 'active')

    if (error || !liabilities) {
      return false
    }

    // Check if any recurring liability is missing required fields
    return liabilities.some(liability => 
      !liability.next_due_date || 
      !liability.recurring_amount || 
      liability.recurring_amount === 0 ||
      !liability.recurring_frequency
    )

  } catch (error) {
    console.error('Error checking migration status:', error)
    return false
  }
}

/**
 * Get summary of recurring payments that need fixing
 */
export async function getRecurringPaymentsMigrationSummary() {
  const supabase = createClient()

  try {
    const { data: liabilities, error } = await supabase
      .from('liabilities')
      .select('id, name, is_recurring, next_due_date, recurring_amount, recurring_frequency')
      .eq('is_recurring', true)
      .eq('status', 'active')

    if (error || !liabilities) {
      return { needsMigration: false, issues: [] }
    }

    const issues = liabilities.map(liability => {
      const problems: string[] = []
      
      if (!liability.next_due_date) {
        problems.push('Missing next due date')
      }
      
      if (!liability.recurring_amount || liability.recurring_amount === 0) {
        problems.push('Missing recurring amount')
      }
      
      if (!liability.recurring_frequency) {
        problems.push('Missing frequency')
      }

      return {
        id: liability.id,
        name: liability.name,
        problems: problems.length > 0 ? problems : null
      }
    }).filter(item => item.problems !== null)

    return {
      needsMigration: issues.length > 0,
      issues,
      total: liabilities.length,
      needsFixing: issues.length
    }

  } catch (error) {
    console.error('Error getting migration summary:', error)
    return { needsMigration: false, issues: [] }
  }
}
