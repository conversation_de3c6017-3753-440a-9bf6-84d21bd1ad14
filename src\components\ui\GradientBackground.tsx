'use client'

import { motion } from 'framer-motion'

interface GradientBackgroundProps {
  children: React.ReactNode
  variant?: 'default' | 'dashboard' | 'auth'
}

function GradientBackground({
  children,
  variant = 'default'
}: GradientBackgroundProps) {
  const variants = {
    default: 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100',
    dashboard: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50',
    auth: 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800'
  }

  return (
    <div className={`min-h-screen ${variants[variant]} relative overflow-hidden`}>
      {/* Static background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl" />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default GradientBackground
