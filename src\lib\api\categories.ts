import { createClient } from '@/lib/supabase/client'
import type { Category, Database } from '@/lib/types/database'

type CategoryInsert = Database['public']['Tables']['categories']['Insert']
type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export async function getCategories(type?: string) {
  const supabase = createClient()
  
  let query = supabase
    .from('categories')
    .select('*')
    .order('name')

  if (type) {
    query = query.eq('type', type)
  }

  const { data, error } = await query

  return { data, error }
}

export async function getCategory(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('id', id)
    .single()

  return { data, error }
}

export async function createCategory(category: CategoryInsert) {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error('User not authenticated')

  const { data, error } = await supabase
    .from('categories')
    .insert({
      ...category,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateCategory(id: string, updates: CategoryUpdate) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteCategory(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('categories')
    .delete()
    .eq('id', id)

  return { error }
}

// Create default categories for new users
export async function createDefaultCategories() {
  const defaultCategories = [
    // Income categories
    { name: 'Salary', type: 'income', color: '#10B981' },
    { name: 'Business Income', type: 'income', color: '#059669' },
    { name: 'Investment Returns', type: 'income', color: '#047857' },
    { name: 'Other Income', type: 'income', color: '#065F46' },
    
    // Expense categories
    { name: 'Food & Dining', type: 'expense', color: '#EF4444' },
    { name: 'Transportation', type: 'expense', color: '#DC2626' },
    { name: 'Shopping', type: 'expense', color: '#B91C1C' },
    { name: 'Entertainment', type: 'expense', color: '#991B1B' },
    { name: 'Bills & Utilities', type: 'expense', color: '#7F1D1D' },
    { name: 'Healthcare', type: 'expense', color: '#F59E0B' },
    { name: 'Education', type: 'expense', color: '#D97706' },
    { name: 'Travel', type: 'expense', color: '#B45309' },
    { name: 'Other Expenses', type: 'expense', color: '#92400E' },
  ]

  const results = []
  for (const category of defaultCategories) {
    const result = await createCategory(category as CategoryInsert)
    results.push(result)
  }

  return results
}
