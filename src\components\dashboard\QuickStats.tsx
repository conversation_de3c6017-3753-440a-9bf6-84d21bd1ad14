import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { AlertTriangle, TrendingUp, CreditCard, Users } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface QuickStatsProps {
  totalAssets: number
  totalLiabilities: number
  totalReceivables: number
  overdueItems: {
    liabilities: number
    receivables: number
  }
}

export default function QuickStats({ 
  totalAssets, 
  totalLiabilities, 
  totalReceivables, 
  overdueItems 
}: QuickStatsProps) {
  const stats = [
    {
      title: 'Total Assets',
      value: formatCurrency(totalAssets),
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Total Liabilities',
      value: formatCurrency(totalLiabilities),
      icon: CreditCard,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: 'Total Receivables',
      value: formatCurrency(totalReceivables),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Overdue Items',
      value: `${overdueItems.liabilities + overdueItems.receivables}`,
      icon: AlertTriangle,
      color: overdueItems.liabilities + overdueItems.receivables > 0 ? 'text-red-600' : 'text-gray-600',
      bgColor: overdueItems.liabilities + overdueItems.receivables > 0 ? 'bg-red-50' : 'bg-gray-50'
    }
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardContent className="p-4 lg:p-6">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-5 w-5 lg:h-6 lg:w-6 ${stat.color}`} />
              </div>
              <div className="ml-3 lg:ml-4 min-w-0 flex-1">
                <p className="text-xs lg:text-sm font-medium text-gray-600 truncate">{stat.title}</p>
                <p className={`text-lg lg:text-2xl font-bold ${stat.color} truncate`}>{stat.value}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
