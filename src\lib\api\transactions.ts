import { createClient } from '@/lib/supabase/client'
import { createAsset, updateAsset } from './assets'
import { createLiability, updateLiability } from './liabilities'
import { createReceivable, updateReceivable } from './receivables'

export interface Transaction {
  id: string
  user_id: string
  category_id: string | null
  type: 'income' | 'expense' | 'transfer'
  amount: number
  description: string | null
  transaction_date: string
  currency: string
  created_at: string
  updated_at: string
  categories?: {
    id: string
    name: string
    color: string
  }
}

type TransactionInsert = {
  category_id?: string | null
  type: 'income' | 'expense' | 'transfer'
  amount: number
  description?: string | null
  transaction_date: string
  currency?: string
}

export async function getTransactions() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('transactions')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .order('transaction_date', { ascending: false })

  return { data, error }
}

export async function createTransaction(transaction: TransactionInsert) {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return { data: null, error: authError || new Error('User not authenticated') }
  }

  const { data, error } = await supabase
    .from('transactions')
    .insert({
      ...transaction,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateTransaction(id: string, updates: Partial<TransactionInsert>) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('transactions')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteTransaction(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('transactions')
    .delete()
    .eq('id', id)

  return { error }
}

// Process transaction and update relevant financial records
export async function processTransaction(transactionData: TransactionInsert & {
  updateAssets?: boolean
  updateLiabilities?: boolean
  updateReceivables?: boolean
  assetName?: string
  liabilityName?: string
  receivableName?: string
  debtorName?: string
}) {
  // Extract only the transaction fields for database insertion
  const {
    updateAssets,
    updateLiabilities,
    updateReceivables,
    assetName,
    liabilityName,
    receivableName,
    debtorName,
    ...transactionFields
  } = transactionData

  const { data: transaction, error: transactionError } = await createTransaction(transactionFields)
  
  if (transactionError || !transaction) {
    return { data: null, error: transactionError }
  }

  // If it's an income transaction and should update assets
  if (transactionData.type === 'income' && transactionData.updateAssets && transactionData.assetName) {
    await createAsset({
      name: transactionData.assetName,
      current_value: transactionData.amount,
      asset_type: 'cash',
      currency: transactionData.currency || 'LKR',
      description: `From transaction: ${transactionData.description || 'Income'}`
    })
  }

  // If it's an expense transaction and should update liabilities
  if (transactionData.type === 'expense' && transactionData.updateLiabilities && transactionData.liabilityName) {
    await createLiability({
      name: transactionData.liabilityName,
      principal_amount: transactionData.amount,
      current_balance: transactionData.amount,
      liability_type: 'other',
      currency: transactionData.currency || 'LKR',
      description: `From transaction: ${transactionData.description || 'Expense'}`
    })
  }

  // If it's a transfer and should update receivables
  if (transactionData.type === 'transfer' && transactionData.updateReceivables && transactionData.receivableName && transactionData.debtorName) {
    await createReceivable({
      debtor_name: transactionData.debtorName,
      principal_amount: transactionData.amount,
      current_balance: transactionData.amount,
      currency: transactionData.currency || 'LKR',
      description: `From transaction: ${transactionData.description || 'Transfer'}`
    })
  }

  return { data: transaction, error: null }
}

export async function getTransactionSummary(startDate?: string, endDate?: string) {
  const supabase = createClient()
  
  let query = supabase
    .from('transactions')
    .select('type, amount, currency')

  if (startDate) {
    query = query.gte('transaction_date', startDate)
  }
  if (endDate) {
    query = query.lte('transaction_date', endDate)
  }

  const { data, error } = await query

  if (error || !data) {
    return { data: null, error }
  }

  const summary = data.reduce((acc, transaction) => {
    const { type, amount } = transaction
    if (!acc[type]) {
      acc[type] = 0
    }
    acc[type] += amount
    return acc
  }, {} as Record<string, number>)

  return { 
    data: {
      income: summary.income || 0,
      expense: summary.expense || 0,
      transfer: summary.transfer || 0,
      netIncome: (summary.income || 0) - (summary.expense || 0)
    }, 
    error: null 
  }
}
