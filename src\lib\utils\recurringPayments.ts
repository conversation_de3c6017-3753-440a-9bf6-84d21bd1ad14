import type { Liability, RecurringFrequency } from '@/lib/types/database'

export interface RecurringPaymentAlert {
  id: string
  name: string
  amount: number
  currency: string
  dueDate: Date
  daysUntilDue: number
  isOverdue: boolean
  frequency: RecurringFrequency
  type: 'liability'
}

export interface UpcomingPayment {
  id: string
  name: string
  amount: number
  currency: string
  paymentDate: Date
  frequency: RecurringFrequency
  type: 'liability'
  liability: Liability
}

/**
 * Calculate the next due date based on current date and frequency
 */
export function calculateNextDueDate(
  currentDate: Date,
  frequency: RecurringFrequency
): Date {
  const nextDate = new Date(currentDate)
  
  switch (frequency) {
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + 7)
      break
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1)
      break
    case 'quarterly':
      nextDate.setMonth(nextDate.getMonth() + 3)
      break
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + 1)
      break
  }
  
  return nextDate
}

/**
 * Get recurring payments that are due within the specified number of days
 */
export function getRecurringPaymentsDue(
  liabilities: Liability[], 
  daysAhead: number = 7
): RecurringPaymentAlert[] {
  const today = new Date()
  const checkDate = new Date()
  checkDate.setDate(today.getDate() + daysAhead)
  
  const alerts: RecurringPaymentAlert[] = []
  
  liabilities.forEach(liability => {
    if (!liability.is_recurring || !liability.next_due_date || !liability.recurring_frequency) {
      return
    }
    
    const dueDate = new Date(liability.next_due_date)
    const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    
    // Include if due within the specified period or overdue
    if (daysUntilDue <= daysAhead) {
      alerts.push({
        id: liability.id,
        name: liability.name,
        amount: liability.recurring_amount || 0,
        currency: liability.currency,
        dueDate,
        daysUntilDue,
        isOverdue: daysUntilDue < 0,
        frequency: liability.recurring_frequency,
        type: 'liability'
      })
    }
  })
  
  return alerts.sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())
}

/**
 * Check if a recurring payment is overdue
 */
export function isRecurringPaymentOverdue(liability: Liability): boolean {
  if (!liability.is_recurring || !liability.next_due_date) {
    return false
  }
  
  const today = new Date()
  const dueDate = new Date(liability.next_due_date)
  
  return dueDate < today
}

/**
 * Generate upcoming payments for a specific period
 */
export function getUpcomingPayments(
  liabilities: Liability[], 
  days: number = 30
): UpcomingPayment[] {
  const today = new Date()
  const endDate = new Date()
  endDate.setDate(today.getDate() + days)
  
  const upcomingPayments: UpcomingPayment[] = []
  
  liabilities.forEach(liability => {
    if (!liability.is_recurring || 
        !liability.recurring_frequency || 
        !liability.next_due_date ||
        liability.status !== 'active') {
      return
    }
    
    let currentDate = new Date(liability.next_due_date)
    
    // Generate upcoming payments within the specified period
    while (currentDate <= endDate) {
      if (currentDate >= today) {
        upcomingPayments.push({
          id: `${liability.id}-${currentDate.getTime()}`,
          name: liability.name,
          amount: liability.recurring_amount || 0,
          currency: liability.currency,
          paymentDate: new Date(currentDate),
          frequency: liability.recurring_frequency,
          type: 'liability',
          liability
        })
      }
      
      currentDate = calculateNextDueDate(currentDate, liability.recurring_frequency)
      
      // Prevent infinite loop - max 100 payments
      if (upcomingPayments.length > 100) break
    }
  })
  
  return upcomingPayments.sort((a, b) => a.paymentDate.getTime() - b.paymentDate.getTime())
}

/**
 * Calculate total monthly recurring payments
 */
export function calculateMonthlyRecurringTotal(liabilities: Liability[]): number {
  let monthlyTotal = 0
  
  liabilities.forEach(liability => {
    if (!liability.is_recurring || 
        !liability.recurring_amount || 
        !liability.recurring_frequency ||
        liability.status !== 'active') {
      return
    }
    
    // Convert all frequencies to monthly equivalent
    switch (liability.recurring_frequency) {
      case 'weekly':
        monthlyTotal += liability.recurring_amount * 4.33 // Average weeks per month
        break
      case 'monthly':
        monthlyTotal += liability.recurring_amount
        break
      case 'quarterly':
        monthlyTotal += liability.recurring_amount / 3
        break
      case 'yearly':
        monthlyTotal += liability.recurring_amount / 12
        break
    }
  })
  
  return monthlyTotal
}

/**
 * Get frequency display text
 */
export function getFrequencyDisplayText(frequency: RecurringFrequency): string {
  const frequencyMap = {
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly'
  }
  
  return frequencyMap[frequency] || frequency
}

/**
 * Update liability after payment is made
 */
export function updateLiabilityAfterPayment(liability: Liability): Partial<Liability> {
  if (!liability.is_recurring || !liability.recurring_frequency || !liability.next_due_date) {
    return liability
  }
  
  const currentDueDate = new Date(liability.next_due_date)
  const nextDueDate = calculateNextDueDate(currentDueDate, liability.recurring_frequency)
  
  return {
    last_payment_date: currentDueDate.toISOString().split('T')[0],
    next_due_date: nextDueDate.toISOString().split('T')[0],
    updated_at: new Date().toISOString()
  }
}

/**
 * Validate recurring payment data
 */
export function validateRecurringPaymentData(data: Partial<Liability>): string[] {
  const errors: string[] = []
  
  if (data.is_recurring) {
    if (!data.recurring_frequency) {
      errors.push('Recurring frequency is required for recurring payments')
    }
    
    if (!data.recurring_amount || data.recurring_amount <= 0) {
      errors.push('Recurring amount must be greater than 0')
    }
    
    if (!data.recurring_start_date) {
      errors.push('Start date is required for recurring payments')
    }
    
    if (data.recurring_end_date && data.recurring_start_date) {
      const startDate = new Date(data.recurring_start_date)
      const endDate = new Date(data.recurring_end_date)
      
      if (endDate <= startDate) {
        errors.push('End date must be after start date')
      }
    }
  }
  
  return errors
}
