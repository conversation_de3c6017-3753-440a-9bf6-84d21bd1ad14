'use client'

import { useState, useEffect, useMemo } from 'react'
import { Edit, Trash2, Plus, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import SearchInput from '@/components/ui/SearchInput'
import FilterDropdown from '@/components/ui/FilterDropdown'
import { getLiabilities, deleteLiability } from '@/lib/api/liabilities'
import { formatCurrency, formatDate } from '@/lib/utils'
import LiabilityForm from './LiabilityForm'
import type { Liability } from '@/lib/types/database'

export default function LiabilityList() {
  const [liabilities, setLiabilities] = useState<Liability[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingLiability, setEditingLiability] = useState<Liability | undefined>()
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    liability_type: '',
    status: '',
    min_balance: '',
    max_balance: '',
    due_date_from: '',
    due_date_to: ''
  })

  useEffect(() => {
    loadLiabilities()
  }, [])

  const loadLiabilities = async () => {
    setLoading(true)
    const { data } = await getLiabilities()
    if (data) setLiabilities(data)
    setLoading(false)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this liability?')) {
      await deleteLiability(id)
      loadLiabilities()
    }
  }

  const handleEdit = (liability: Liability) => {
    setEditingLiability(liability)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingLiability(undefined)
    loadLiabilities()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingLiability(undefined)
  }

  // Filter and search liabilities
  const filteredLiabilities = useMemo(() => {
    return liabilities.filter(liability => {
      // Search filter
      const matchesSearch = searchTerm === '' ||
        liability.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        liability.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        liability.liability_type.toLowerCase().includes(searchTerm.toLowerCase())

      // Type filter
      const matchesType = filters.liability_type === '' || liability.liability_type === filters.liability_type

      // Status filter
      const matchesStatus = filters.status === '' || liability.status === filters.status

      // Balance filters
      const matchesMinBalance = filters.min_balance === '' || liability.current_balance >= parseFloat(filters.min_balance)
      const matchesMaxBalance = filters.max_balance === '' || liability.current_balance <= parseFloat(filters.max_balance)

      // Date filters
      const matchesFromDate = filters.due_date_from === '' ||
        !liability.due_date ||
        new Date(liability.due_date) >= new Date(filters.due_date_from)
      const matchesToDate = filters.due_date_to === '' ||
        !liability.due_date ||
        new Date(liability.due_date) <= new Date(filters.due_date_to)

      return matchesSearch && matchesType && matchesStatus && matchesMinBalance && matchesMaxBalance && matchesFromDate && matchesToDate
    })
  }, [liabilities, searchTerm, filters])

  const totalBalance = filteredLiabilities
    .filter(l => l.status === 'active')
    .reduce((sum, liability) => sum + liability.current_balance, 0)

  const overdueLiabilities = filteredLiabilities.filter(l =>
    l.status === 'active' &&
    l.due_date &&
    new Date(l.due_date) < new Date()
  )

  const filterOptions = [
    {
      key: 'liability_type',
      label: 'Liability Type',
      type: 'select' as const,
      options: [
        { value: 'loan_taken', label: 'Loan Taken' },
        { value: 'credit_card', label: 'Credit Card' },
        { value: 'mortgage', label: 'Mortgage' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: 'active', label: 'Active' },
        { value: 'paid_off', label: 'Paid Off' },
        { value: 'defaulted', label: 'Defaulted' }
      ]
    },
    {
      key: 'min_balance',
      label: 'Minimum Balance',
      type: 'number' as const
    },
    {
      key: 'max_balance',
      label: 'Maximum Balance',
      type: 'number' as const
    },
    {
      key: 'due_date_from',
      label: 'Due Date From',
      type: 'date' as const
    },
    {
      key: 'due_date_to',
      label: 'Due Date To',
      type: 'date' as const
    }
  ]

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleClearFilters = () => {
    setFilters({
      liability_type: '',
      status: '',
      min_balance: '',
      max_balance: '',
      due_date_from: '',
      due_date_to: ''
    })
    setSearchTerm('')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600'
      case 'paid_off': return 'text-blue-600'
      case 'defaulted': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  if (showForm) {
    return (
      <div className="flex justify-center">
        <LiabilityForm
          liability={editingLiability}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Liabilities</h1>
          <p className="text-gray-600">
            Total Outstanding: {formatCurrency(totalBalance)}
            {filteredLiabilities.length !== liabilities.length && (
              <span className="text-sm text-gray-500 ml-2">
                ({filteredLiabilities.length} of {liabilities.length} shown)
              </span>
            )}
          </p>
          {overdueLiabilities.length > 0 && (
            <p className="text-red-600 flex items-center mt-1">
              <AlertTriangle className="h-4 w-4 mr-1" />
              {overdueLiabilities.length} overdue liability(ies)
            </p>
          )}
        </div>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Liability
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search liabilities..."
          className="flex-1"
        />
        <FilterDropdown
          filters={filterOptions}
          values={filters}
          onChange={handleFilterChange}
          onClear={handleClearFilters}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Liabilities</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredLiabilities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {liabilities.length === 0
                ? "No liabilities found. Add your first liability to get started."
                : "No liabilities match your search criteria."
              }
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Principal</TableHead>
                      <TableHead>Current Balance</TableHead>
                      <TableHead>Interest Rate</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLiabilities.map((liability) => {
                      const isOverdue = liability.status === 'active' &&
                                      liability.due_date &&
                                      new Date(liability.due_date) < new Date()

                      return (
                        <TableRow key={liability.id} className={isOverdue ? 'bg-red-50' : ''}>
                          <TableCell className="font-medium">
                            {liability.name}
                            {isOverdue && <AlertTriangle className="h-4 w-4 text-red-500 inline ml-2" />}
                          </TableCell>
                          <TableCell className="capitalize">
                            {liability.liability_type.replace('_', ' ')}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(liability.principal_amount, liability.currency)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(liability.current_balance, liability.currency)}
                          </TableCell>
                          <TableCell>
                            {liability.interest_rate ? `${liability.interest_rate}%` : '-'}
                          </TableCell>
                          <TableCell>
                            {liability.due_date ? formatDate(liability.due_date) : '-'}
                          </TableCell>
                          <TableCell>
                            <span className={`capitalize ${getStatusColor(liability.status)}`}>
                              {liability.status.replace('_', ' ')}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEdit(liability)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDelete(liability.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="lg:hidden space-y-4">
                {filteredLiabilities.map((liability) => {
                  const isOverdue = liability.status === 'active' &&
                                  liability.due_date &&
                                  new Date(liability.due_date) < new Date()

                  return (
                    <div key={liability.id} className={`bg-white border rounded-lg p-4 shadow-sm ${isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-medium text-gray-900 flex items-center">
                            {liability.name}
                            {isOverdue && <AlertTriangle className="h-4 w-4 text-red-500 ml-2" />}
                          </h3>
                          <p className="text-sm text-gray-500 capitalize">{liability.liability_type.replace('_', ' ')}</p>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(liability)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(liability.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Principal:</span>
                          <span className="text-sm font-medium">{formatCurrency(liability.principal_amount, liability.currency)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Current Balance:</span>
                          <span className="text-sm font-medium">{formatCurrency(liability.current_balance, liability.currency)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Interest Rate:</span>
                          <span className="text-sm font-medium">{liability.interest_rate ? `${liability.interest_rate}%` : '-'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Due Date:</span>
                          <span className="text-sm font-medium">{liability.due_date ? formatDate(liability.due_date) : '-'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Status:</span>
                          <span className={`text-sm font-medium capitalize ${getStatusColor(liability.status)}`}>
                            {liability.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </>
          )}
          )}
        </CardContent>
      </Card>
    </div>
  )
}
