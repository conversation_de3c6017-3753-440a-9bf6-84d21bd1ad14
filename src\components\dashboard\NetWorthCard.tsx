import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { TrendingUp, TrendingDown } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface NetWorthCardProps {
  totalAssets: number
  totalLiabilities: number
  totalReceivables: number
  netWorth: number
}

export default function NetWorthCard({ 
  totalAssets, 
  totalLiabilities, 
  totalReceivables, 
  netWorth 
}: NetWorthCardProps) {
  const isPositive = netWorth >= 0

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle className="flex items-center">
          Net Worth
          {isPositive ? (
            <TrendingUp className="ml-2 h-5 w-5 text-green-500" />
          ) : (
            <TrendingDown className="ml-2 h-5 w-5 text-red-500" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-3xl font-bold">
            <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
              {formatCurrency(netWorth)}
            </span>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-gray-500">Assets</div>
              <div className="font-semibold text-green-600">
                {formatCurrency(totalAssets)}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-gray-500">Receivables</div>
              <div className="font-semibold text-blue-600">
                {formatCurrency(totalReceivables)}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-gray-500">Liabilities</div>
              <div className="font-semibold text-red-600">
                {formatCurrency(totalLiabilities)}
              </div>
            </div>
          </div>
          
          <div className="text-xs text-gray-500 text-center">
            Net Worth = Assets + Receivables - Liabilities
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
